# removed misplaced future import
try:
    import pandas as pd
except ImportError:
    pd = None
import base64
import csv
import json
import os
from io import String<PERSON>
from typing import Dict, List, Optional, Any
from logger import get_logger
from datetime import datetime

logger = get_logger(__name__)

class DataTransformer:
    def __init__(self, mapping_file: Optional[str] = None):
        self.mapping_file = mapping_file
        self.logger = get_logger(__name__)

    def transform_responses(self, input_path: str):
        """Transformace odpovědí do long formátu"""
        if pd is None:
            raise ImportError("pandas library is required for data transformation. Please install pandas")
        # redundant future import removed
        try:
            if not self.mapping_file:
                raise ValueError("Mapping file is required")
                
            df = pd.read_csv(input_path, sep=';')
            mapping_df = pd.read_csv(self.mapping_file)
            
            # Vytvoření long formátu
            question_cols = mapping_df['code'].tolist()
            id_cols = [col for col in df.columns if col not in question_cols]
            
            long_df = pd.melt(df, 
                             id_vars=id_cols,
                             value_vars=question_cols,
                             var_name='question_code',
                             value_name='response')
            
            # Připojení názvů otázek
            long_df = long_df.merge(mapping_df[['code', 'original_name', 'is_main_question']], 
                                   left_on='question_code',
                                   right_on='code',
                                   how='left')
            
            return long_df.drop('code', axis=1)
            
        except Exception as e:
            self.logger.error(f"Chyba při transformaci odpovědí: {str(e)}")
            raise

    def prepare_chart_data(self, df, question_type: Optional[str] = None) -> Dict:
        """Příprava dat pro graf"""
        try:
            if question_type not in ['single_choice', 'multiple_choice', 'numeric']:
                question_type = 'single_choice'
                
            chart_data = {
                'chart_type': question_type,
                'data': []
            }
            
            # Agregace dat podle typu otázky
            if question_type == 'single_choice':
                response_counts = df['response'].value_counts()
                for response, count in response_counts.items():
                    chart_data['data'].append({
                        'label': response,
                        'value': int(count)
                    })
                    
            return chart_data
            
        except Exception as e:
            self.logger.error(f"Chyba při přípravě dat pro graf: {str(e)}")
            raise

    def save_transformed_data(self, df, output_path: str) -> None:
        """Uložení transformovaných dat"""
        try:
            df.to_csv(output_path, index=False)
            self.logger.info(f"Data uložena do {output_path}")
        except Exception as e:
            self.logger.error(f"Chyba při ukládání dat: {str(e)}")
            raise

    def generate_mapping_file(self, lss_path: str, output_path: str) -> None:
        """Generování mapovacího souboru"""
        try:
            generate_question_mapping(self.mapping_file, lss_path, output_path)
            self.logger.info(f"Mapovací soubor vygenerován: {output_path}")
        except Exception as e:
            self.logger.error(f"Chyba při generování mapování: {str(e)}")
            raise

def decompress_csv(file_path: str) -> None:
    """Dekomprese binárního CSV na textový formát"""
    try:
        with open(file_path, 'rb') as f:
            content = f.read()
            
        # Dekódování z base64
        decoded = base64.b64decode(content).decode('utf-8')
        
        # Odstranění BOM znaku
        fixed_content = decoded.lstrip('\ufeff')
        
        # Zpracování CSV po řádcích
        rows = []
        reader = csv.reader(StringIO(fixed_content), delimiter=';', quotechar='"')
        
        for row in reader:
            processed_row = []
            for field in row:
                if field == 'N/A' or not field.strip():
                    processed_row.append('""')
                else:
                    # Zachování přesného formátu textu
                    field = field.strip()
                    # Nahrazení různých typů zalomení řádků za \n
                    field = field.replace('\r\n', '\n').replace('\r', '\n')
                    # Escapování uvozovek a obalení pole
                    if '"' in field or '\n' in field or ';' in field:
                        field = f'"{field.replace('"', '""')}"'
                    else:
                        field = f'"{field}"'
                    processed_row.append(field)
            rows.append(';'.join(processed_row))
        
        # Přepsání opraveného obsahu
        with open(file_path, 'w', encoding='utf-8', newline='') as f:
            f.write('\n'.join(rows))
            
        logger.info(f"Soubor {file_path} úspěšně dekomprimován")
        
    except Exception as e:
        logger.error(f"Chyba při dekompresi CSV: {str(e)}")

def strip_html(text: str) -> str:
    """Odstranění HTML značek z textu"""
    from html.parser import HTMLParser
    
    class MLStripper(HTMLParser):
        def __init__(self):
            super().__init__()
            self.reset()
            self.strict = False
            self.convert_charrefs = True
            self.text = []
            
        def handle_data(self, d):
            self.text.append(d)
            
        def get_data(self):
            return ''.join(self.text)
    
    s = MLStripper()
    s.feed(text)
    return s.get_data().strip()

def generate_question_mapping(csv_path: str, lss_path: str, output_path: str, apply_privacy_filter: bool = True) -> bool:
    """Generování mapování otázek z CSV a LSS souborů s podporou otázek typu pole a privacy filtrování"""
    try:
        import json

        # Načtení hlaviček z CSV
        with open(csv_path, 'r', encoding='utf-8') as f:
            reader = csv.reader(f, delimiter=';')
            headers = next(reader)

        # Filtrace pouze otázek (začínají na G nebo r - pro různé lokality)
        all_question_codes = [h.strip('"') for h in headers if h.strip('"').startswith(('G', 'r'))]

        # OPRAVA: Aplikace privacy filtru na seznam otázek
        question_codes = all_question_codes
        if apply_privacy_filter:
            try:
                # Extrakce survey_id z cesty
                import re
                survey_id_match = re.search(r'(\d{6})', csv_path)
                if survey_id_match:
                    survey_id = survey_id_match.group(1)
                    from privacy_filter import get_privacy_filter
                    privacy_filter = get_privacy_filter(survey_id)

                    if privacy_filter.excluded_columns:
                        # Načteme celý CSV pro mapování sloupců na indexy
                        import pandas as pd
                        df_headers = pd.read_csv(csv_path, sep=';', nrows=0)
                        excluded_column_names = [df_headers.columns[i-1] for i in privacy_filter.excluded_columns
                                               if 0 <= i-1 < len(df_headers.columns)]

                        # Odfiltrujeme vyloučené otázky
                        question_codes = [code for code in all_question_codes if code not in excluded_column_names]
                        logger.info(f"Privacy filtr: vyloučeno {len(all_question_codes) - len(question_codes)} otázek z mapování")
                else:
                    logger.warning("Nepodařilo se extrahovat survey_id pro privacy filtr v question_mapping")
            except ImportError:
                logger.warning("Privacy filter není dostupný pro question_mapping")
            except Exception as e:
                logger.warning(f"Chyba při aplikaci privacy filtru v question_mapping: {e}")
        
        # Načtení a parsování LSS souboru
        with open(lss_path, 'r', encoding='utf-8') as f:
            lss_data = json.load(f)
            
        # Vytvoření slovníku otázek s fallback hodnotami
        question_names = {code: code for code in question_codes}
        question_texts = {code: code for code in question_codes}
        question_groups = {code: "Neznámá skupina" for code in question_codes}  # Mapování kódů na skupiny

        # Vytvoření mapy QID -> hlavní otázka pro subotázky
        qid_to_main_question = {}
        main_questions = {}  # {main_code: question_text}
        
        # KRITICKÁ OPRAVA: Seřazení všech otázek podle group_order a question_order
        # Zachováváme přesné pořadí z LimeSurvey
        all_questions = []

        # Seřadíme skupiny podle group_order
        sorted_groups = sorted(lss_data['groups'], key=lambda g: int(g.get('group_order', 999)))

        for group in sorted_groups:
            group_order = group.get('group_order', 999)
            group_questions = group.get('questions', [])

            # Seřadíme otázky ve skupině podle question_order nebo qid
            def get_question_order(q):
                props = q.get('properties', {})
                if 'question_order' in props:
                    return int(props['question_order'])
                return int(q.get('qid', 999999))

            sorted_questions = sorted(group_questions, key=get_question_order)

            for question in sorted_questions:
                qid = question.get('qid', 0)
                title = question.get('title', '')
                question_text = strip_html(question.get('question', ''))
                qtype = question.get('type', '')
                all_questions.append((qid, title, qtype, question_text, question, group_order))
        
        # Seřazení podle QID
        all_questions.sort()
        
        # První průchod - identifikace hlavních otázek a jejich QID rozsahů
        current_main_question = None

        for qid, title, qtype, question_text, question, group_order in all_questions:
            # OPRAVA: Rozšířené typy hlavních otázek
            # M = Multiple choice, L = List, T = Long free text, F = Array, A = Array (5 point choice),
            # B = Array (10 point choice), C = Array (Yes/No/Uncertain), E = Array (Increase/Same/Decrease)
            main_question_types = ['M', 'L', 'T', 'F', 'A', 'B', 'C', 'E', 'H', 'P', 'Q', 'R', 'S', 'U', 'X', 'Y', '!', '1', ':', ';', '|']
            # KRITICKÁ OPRAVA: Rozpoznávání hlavních otázek nejen podle "G" ale i podle "r" (pro různé lokality)
            if (title.startswith('G') or title.startswith('r')) and qtype in main_question_types:
                current_main_question = title

                # Najdeme skupinu pro tuto otázku
                group_name = "Neznámá skupina"
                for group in sorted_groups:
                    if any(q.get('title') == title for q in group.get('questions', [])):
                        group_name = group.get('group_name', 'Neznámá skupina')
                        break

                # OPRAVA: Lepší zpracování prázdných otázek
                if question_text and question_text.strip():
                    main_questions[title] = question_text
                else:
                    # Pro prázdné otázky použijeme název z group description nebo fallback
                    fallback_name = f"Otázka {title}"
                    main_questions[title] = fallback_name
                    logger.debug(f"   Prázdná hlavní otázka {title}, použit fallback: '{fallback_name}'")

                # Pokud je to přímo v CSV, přidej
                if title in question_codes:
                    # Přiřadíme skupinu
                    question_groups[title] = group_name

                    if question_text and question_text.strip():
                        question_names[title] = question_text
                        question_texts[title] = question_text
                    else:
                        # Pro prázdné otázky zkusíme použít název skupiny
                        if group_name and group_name.strip() and group_name != "Neznámá skupina":
                            question_names[title] = group_name
                            question_texts[title] = group_name
                            logger.info(f"   Použit název skupiny pro {title}: '{group_name}'")
                        else:
                            question_names[title] = f"Otázka {title}"
                            question_texts[title] = f"Otázka {title}"
            
            # Subotázky (SQ001, SQ002, atd.) patří k poslednímu hlavnímu
            elif title.startswith('SQ') and current_main_question:
                qid_to_main_question[qid] = current_main_question
        
        # Druhý průchod - mapování subotázek na CSV sloupce s OPRAVOU názvů
        logger.info("🔍 Mapování subotázek a speciálních otázek...")

        # Definice typů hlavních otázek pro druhý průchod
        main_question_types = ['M', 'L', 'T', 'F', 'A', 'B', 'C', 'E', 'H', 'P', 'Q', 'R', 'S', 'U', 'X', 'Y', '!', '1', ':', ';', '|']

        logger.info(f"Celkem skupin k procházení: {len(sorted_groups)}")
        for group in sorted_groups:  # Použijeme seřazené skupiny
            group_name = group.get('group_name', 'Neznámá skupina')
            logger.info(f"Procházím skupinu: '{group_name}' s {len(group.get('questions', []))} otázkami")

            for question in group['questions']:
                qid = question.get('qid')
                title = question.get('title', '')
                qtype = question.get('type', '')
                question_text = strip_html(question.get('question', ''))

                logger.debug(f"  Otázka: {title} (typ: {qtype}) ve skupině '{group_name}'")

                # KRITICKÁ OPRAVA: Pokud je to subotázka, použij správný text
                if title.startswith('SQ') and qid in qid_to_main_question:
                    main_question = qid_to_main_question[qid]
                    csv_code = f"{main_question}[{title}]"

                    if csv_code in question_codes:
                        question_groups[csv_code] = group_name

                        # OPRAVA: Použij question_text místo csv_code jako název
                        if question_text and question_text.strip():
                            question_names[csv_code] = question_text
                            question_texts[csv_code] = question_text
                            logger.debug(f"   Subotázka {csv_code} -> '{question_text}' (skupina: {group_name})")
                        else:
                            # Fallback pokud question_text je prázdný
                            question_names[csv_code] = title
                            question_texts[csv_code] = title
                            logger.warning(f"   Subotázka {csv_code} nemá text, použit kód: '{title}'")

                # KRITICKÁ OPRAVA: Mapování hlavních otázek s kódy "r" na správné skupiny
                elif title.startswith('r') and title in question_codes:
                    question_groups[title] = group_name
                    if question_text and question_text.strip():
                        question_names[title] = question_text
                        question_texts[title] = question_text
                        logger.debug(f"   Hlavní otázka {title} -> '{question_text}' (skupina: {group_name})")
                    else:
                        question_names[title] = f"Otázka {title}"
                        question_texts[title] = f"Otázka {title}"

                # KRITICKÁ OPRAVA: Mapování všech subotázek hlavních otázek na správné skupiny
                # Pro každou hlavní otázku (G nebo r) najdeme všechny její subotázky v CSV a přiřadíme jim skupinu
                logger.debug(f"   Kontroluji podmínku pro {title}: startswith('r')={title.startswith('r')}, startswith('G')={title.startswith('G')}, qtype={qtype}, in main_question_types={qtype in main_question_types}")
                if (title.startswith('r') or title.startswith('G')) and qtype in main_question_types:
                    logger.info(f"   ✅ Nalezena hlavní otázka {title} (typ: {qtype}) ve skupině '{group_name}'")
                    # Pro každou hlavní otázku najdeme všechny její subotázky v CSV
                    mapped_count = 0
                    for code in question_codes:
                        if code.startswith(f"{title}["):
                            question_groups[code] = group_name
                            mapped_count += 1
                            logger.info(f"   ✅ Mapována skupina pro subotázku {code} -> '{group_name}'")
                    if mapped_count > 0:
                        logger.info(f"   ✅ Celkem namapováno {mapped_count} subotázek pro {title}")
                    else:
                        logger.debug(f"   ⚠️  Žádné subotázky nenalezeny pro {title}")
                else:
                    if title.startswith('r') or title.startswith('G'):
                        logger.debug(f"   ❌ Otázka {title} (typ: {qtype}) nesplňuje podmínku - typ není v main_question_types")
                    else:
                        logger.debug(f"   ❌ Otázka {title} nezačína 'r' ani 'G'")

            # OPRAVA: Zpracování "other" otázek s lepším názvem (mimo hlavní smyčku)
            for question in group['questions']:
                qid = question.get('qid')
                title = question.get('title', '')
                qtype = question.get('type', '')
                question_text = strip_html(question.get('question', ''))

                if title.startswith('G') and title.endswith('[other]'):
                    if title in question_codes:
                        if question_text and question_text.strip():
                            question_names[title] = question_text
                            question_texts[title] = question_text
                        else:
                            # Vytvoříme lepší název pro "other" otázky
                            base_code = title.replace('[other]', '')
                            if base_code in main_questions:
                                question_names[title] = f"{main_questions[base_code]} (jiné)"
                                question_texts[title] = f"{main_questions[base_code]} (jiné)"
                            else:
                                question_names[title] = f"{title} (jiné)"
                                question_texts[title] = f"{title} (jiné)"
        
        # Třetí průchod - zpracování speciálních případů (other, atd.)
        for code in question_codes:
            if '[other]' in code and question_names[code] == code:
                # Pokud nemáme text pro "other" otázku, použij název hlavní otázky
                main_code = code.replace('[other]', '')
                if main_code in main_questions:
                    question_names[code] = f"{main_questions[main_code]} (jiné)"
                    question_texts[code] = f"{main_questions[main_code]} (jiné)"

        # OPRAVA: Finální kontrola a oprava prázdných názvů
        empty_names_count = 0
        for code in question_codes:
            if question_names[code] == code:  # Pokud název je stejný jako kód
                empty_names_count += 1

                # Pokusíme se najít lepší název z kontextu
                if '[' in code and ']' in code:  # Je to subotázka
                    main_code = code.split('[')[0]

                    if main_code in main_questions and main_questions[main_code]:
                        # OPRAVA: Použijeme pouze název hlavní otázky BEZ přidání kódu subotázky
                        question_names[code] = main_questions[main_code]
                        question_texts[code] = main_questions[main_code]
                        logger.info(f"   Opraveno: {code} -> '{question_names[code]}'")
                    else:
                        logger.warning(f"   Subotázka {code} nemá mapovaný název, zůstává kód")
                else:
                    logger.warning(f"   Hlavní otázka {code} nemá mapovaný název, zůstává kód")

        if empty_names_count > 0:
            logger.warning(f"⚠️  {empty_names_count} otázek nemá správně mapované názvy")
        else:
            logger.info("✅ Všechny otázky mají správně mapované názvy")
        
        # Generování výstupního CSV s question_text sloupcem a informací o skupině
        with open(output_path, 'w', encoding='utf-8', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['question_code', 'question_name', 'question_text', 'is_main_question', 'group_name', 'original_title', 'question_type'])

            # KRITICKÁ OPRAVA: Seřadíme otázky podle pořadí z LimeSurvey
            # Vytvoříme mapování kódů na pořadí z LSS struktury
            code_to_order = {}

            for qid, title, qtype, question_text, question, group_order in all_questions:
                # Pro hlavní otázky (G nebo r kódy)
                if (title.startswith('G') or title.startswith('r')):
                    if title in question_codes:
                        code_to_order[title] = (group_order, qid, 0)  # 0 = hlavní otázka

                # Pro subotázky
                elif title.startswith('SQ') and qid in qid_to_main_question:
                    main_question = qid_to_main_question[qid]
                    csv_code = f"{main_question}[{title}]"
                    if csv_code in question_codes:
                        code_to_order[csv_code] = (group_order, qid, 1)  # 1 = subotázka

            # Seřadíme podle group_order, pak qid, pak typ (hlavní/sub)
            def get_sort_key(code):
                if code in code_to_order:
                    return code_to_order[code]
                # Fallback pro kódy bez mapování
                return (999, 999999, 1 if '[' in code else 0)

            sorted_codes = sorted(question_codes, key=get_sort_key)
            logger.info(f"✅ Otázky seřazeny podle LimeSurvey pořadí")

            for code in sorted_codes:
                # Rozlišení hlavních otázek a subotázek
                is_main = '[' not in code

                # Získání typu otázky a původního názvu
                original_title = code.split('[')[1].rstrip(']') if '[' in code else code
                question_type = 'T'  # Výchozí typ

                # Najdeme typ otázky z LSS struktury
                for qid, title, qtype, question_text, question, group_order in all_questions:
                    if title == code or (is_main and title == code) or (not is_main and title == original_title):
                        question_type = qtype
                        break

                writer.writerow([code, question_names[code], question_texts[code], is_main, question_groups[code], original_title, question_type])

        logger.info(f"Vygenerováno mapování pro {len(question_codes)} otázek")
        return True
        
    except Exception as e:
        logger.error(f"Chyba při generování mapování otázek: {str(e)}")
        return False

def _filter_completed_responses(df):
    """Filtruje pouze dokončené odpovědi"""
    if pd is None:
        return df

    try:
        original_count = len(df)

        # Najdeme sloupce s submitdate a lastpage (mohou mít mezery a uvozovky)
        submitdate_col = None
        lastpage_col = None

        for col in df.columns:
            col_clean = col.strip().strip('"').strip()
            if col_clean == 'submitdate':
                submitdate_col = col
            elif col_clean == 'lastpage':
                lastpage_col = col

        # Metoda 1: Kontrola submitdate (pokud existuje a není anonymizováno)
        if submitdate_col is not None:
            # Dokončené záznamy mají vyplněné submitdate
            completed_df = df[df[submitdate_col].notna() & (df[submitdate_col] != '')]
            if len(completed_df) > 0:
                logger.info(f"Filtrování podle submitdate: {len(completed_df)} z {original_count} záznamů")
                return completed_df
            else:
                logger.warning("Žádné záznamy s vyplněným submitdate - možná anonymizovaný průzkum")

        # Metoda 2: Kontrola lastpage (fallback pro anonymní průzkumy)
        if lastpage_col is not None:
            # Najdeme nejvyšší číslo stránky
            max_page = df[lastpage_col].max()
            if pd.notna(max_page) and max_page > 0:
                # Dokončené záznamy mají lastpage = max_page
                completed_df = df[df[lastpage_col] == max_page]
                logger.info(f"Filtrování podle lastpage (max={max_page}): {len(completed_df)} z {original_count} záznamů")
                return completed_df
            else:
                logger.warning("Žádné platné hodnoty v lastpage")

        # Metoda 3: Fallback - pokud nejsou dostupné indikátory dokončení
        logger.warning("Nejsou dostupné indikátory dokončení (submitdate ani lastpage)")
        logger.warning("Zpracovávají se všechny záznamy")
        return df

    except Exception as e:
        logger.error(f"Chyba při filtrování dokončených záznamů: {e}")
        logger.warning("Zpracovávají se všechny záznamy")
        return df

def transform_to_long_format(csv_path: str, mapping_path: str, output_path: str, filter_completed_only: bool = True, 
                           apply_privacy_filter: bool = True) -> bool:
    """Transformace CSV dat na long formát s možností privacy filtrování"""
    if pd is None:
        logger.error("Pandas knihovna není dostupná. Pro transformaci dat je potřeba nainstalovat pandas.")
        return False

    try:
        # Načtení dat
        df = pd.read_csv(csv_path, sep=';')
        
        # Aplikace privacy filtru pokud je požadován
        if apply_privacy_filter:
            try:
                # Extrakce survey_id z cesty
                import re
                survey_id_match = re.search(r'(\d{6})', csv_path)
                if survey_id_match:
                    survey_id = survey_id_match.group(1)
                    from privacy_filter import apply_privacy_filter as apply_filter
                    df = apply_filter(df, survey_id)
                    logger.info(f"Privacy filtr aplikován - zpracováváno {len(df.columns)} sloupců")
                else:
                    logger.warning("Nepodařilo se extrahovat survey_id pro privacy filtr")
            except ImportError:
                logger.warning("Privacy filter není dostupný - zpracovávají se všechna data")
            except Exception as e:
                logger.warning(f"Chyba při aplikaci privacy filtru: {e} - zpracovávají se všechna data")
        
        mapping_df = pd.read_csv(mapping_path)

        original_count = len(df)
        logger.info(f"Načteno {original_count} záznamů z CSV")

        # Filtrování dokončených záznamů
        if filter_completed_only:
            df_filtered = _filter_completed_responses(df)
            filtered_count = len(df_filtered)
            logger.info(f"Po filtrování dokončených záznamů: {filtered_count} z {original_count}")
            df = df_filtered
        else:
            logger.info("Zpracovávají se všechny záznamy (včetně nedokončených)")
        
        # Vytvoření long formátu - pouze sloupce, které skutečně existují v CSV
        all_question_codes = mapping_df['question_code'].tolist()
        question_cols = [col for col in all_question_codes if col in df.columns]
        id_cols = [col for col in df.columns if col not in question_cols]
        
        # Log informace o chybějících sloupcích
        missing_cols = [col for col in all_question_codes if col not in df.columns]
        if missing_cols:
            logger.info(f"Následující otázky z mapování nejsou v CSV (pravděpodobně hlavní otázky polí): {missing_cols}")
        
        # Převod na long formát
        long_df = pd.melt(df, 
                         id_vars=id_cols,
                         value_vars=question_cols,
                         var_name='question_code',
                         value_name='response')
        
        # Připojení názvů otázek
        long_df = long_df.merge(mapping_df[['question_code', 'question_name', 'question_text', 'is_main_question']],
                               left_on='question_code',
                               right_on='question_code',
                               how='left')
        
        # Úprava a export (už nemusíme dropovat 'code' sloupec)
        long_df['response'] = long_df['response'].fillna('')
        long_df.to_csv(output_path, index=False)
        
        logger.info(f"Data úspěšně transformována do long formátu: {output_path}")
        return True
        
    except Exception as e:
        logger.error(f"Chyba při transformaci do long formátu: {str(e)}")
        return False

def generate_chart_data(long_path: str, output_path: str, apply_privacy_filter: bool = True) -> bool:
    """Generování datové struktury pro grafy podle LimeSurvey logiky s privacy filtrováním"""
    if pd is None:
        logger.error("Pandas knihovna není dostupná. Pro generování dat pro grafy je potřeba nainstalovat pandas.")
        return False
        
    try:
        import os
        # Načtení long formátu
        df = pd.read_csv(long_path)
        
        # Aplikace privacy filtru pokud je požadován
        if apply_privacy_filter:
            try:
                # Extrakce survey_id z cesty
                import re
                survey_id_match = re.search(r'(\d{6})', long_path)
                if survey_id_match:
                    survey_id = survey_id_match.group(1)
                    from privacy_filter import get_privacy_filter
                    privacy_filter = get_privacy_filter(survey_id)
                    
                    # Filtrujeme question_codes podle vyloučených sloupců
                    if privacy_filter.excluded_columns:
                        # Načteme původní CSV pro mapování sloupců
                        original_csv_path = long_path.replace('responses_long.csv', 'responses.csv')
                        if os.path.exists(original_csv_path):
                            original_df = pd.read_csv(original_csv_path, sep=';', nrows=0)  # Jen hlavička
                            excluded_column_names = [original_df.columns[i-1] for i in privacy_filter.excluded_columns 
                                                   if 0 <= i-1 < len(original_df.columns)]
                            
                            # Odfiltrujeme otázky odpovídající vyloučeným sloupcům
                            df = df[~df['question_code'].isin(excluded_column_names)]
                            logger.info(f"Privacy filtr: vyloučeno {len(excluded_column_names)} typů otázek z chart_data")
                else:
                    logger.warning("Nepodařilo se extrahovat survey_id pro privacy filtr")
            except ImportError:
                logger.warning("Privacy filter není dostupný - zpracovávají se všechna data")
            except Exception as e:
                logger.warning(f"Chyba při aplikaci privacy filtru: {e} - zpracovávají se všechna data")
        
        # Agregace dat pro grafy
        chart_data = []
        
        # Identifikace hlavních otázek polí a jejich seskupení
        main_field_questions = {}  # {main_code: [subquestion_codes]}
        standalone_questions = []
        
        all_questions = df['question_code'].unique()
        
        for question in all_questions:
            # Detekce subotázek polí (obsahují [SQ...])
            if '[SQ' in question:
                # Extrakce hlavního kódu otázky (např. G6Q00001 z G6Q00001[SQ001])
                main_code = question.split('[')[0]
                if main_code not in main_field_questions:
                    main_field_questions[main_code] = []
                main_field_questions[main_code].append(question)
            else:
                standalone_questions.append(question)
        
        # Zpracování samostatných otázek (ne polí)
        for question in standalone_questions:
            question_df = df[df['question_code'] == question]
            
            # Detekce typu otázky na základě dat
            question_name = question_df['question_name'].iloc[0]
            unique_responses = question_df['response'].nunique()
            
            # Heuristika pro určení typu otázky
            question_type = 'single_choice'  # Výchozí
            if unique_responses > 10:
                question_type = 'scale'  # Škálové otázky
            elif any(resp for resp in question_df['response'].unique() if resp and len(str(resp)) > 50):
                question_type = 'text'  # Textové odpovědi
            
            # Základní informace o otázce
            question_info = {
                'code': question,
                'name': question_name,
                'type': question_type,
                'data': []
            }
            
            # OPRAVA: Počty odpovědí s logickým pořadím
            response_counts = question_df['response'].value_counts()

            # Získání logického pořadí odpovědí
            logical_order = _get_logical_response_order(list(response_counts.keys()))

            # Použití logického pořadí místo value_counts pořadí
            for response in logical_order:
                count = response_counts.get(response, 0)
                if count > 0 and response:  # Ignorování prázdných odpovědí
                    question_info['data'].append({
                        'label': response,
                        'value': int(count)
                    })
            
            chart_data.append(question_info)
        
        # Zpracování otázek polí - JEDEN graf pro celou hlavní otázku
        for main_code, subquestions in main_field_questions.items():
            # Získání názvu hlavní otázky z LSS struktury
            main_question_name = _get_array_question_title_from_lss(main_code, long_path)

            # Pokus o získání lepšího názvu z mapování jako fallback
            if main_question_name.startswith("Otázka pole:"):
                try:
                    # Načtení question_mapping pro lepší název
                    mapping_path = long_path.replace('responses_long.csv', 'question_mapping.csv')
                    if os.path.exists(mapping_path):
                        mapping_df = pd.read_csv(mapping_path)
                        main_mapping = mapping_df[mapping_df['question_code'] == main_code]
                        if not main_mapping.empty:
                            main_question_name = main_mapping['question_name'].iloc[0]
                except:
                    pass  # Použij výchozí název
            
            # Vytvoření kontingenční tabulky pro otázku pole
            field_data = []
            all_responses = set()
            
            # OPRAVA: Získání LSS pořadí odpovědí místo náhodného set()
            lss_ordered_responses = _get_lss_answer_order(main_code, long_path)

            # Fallback - sběr všech možných odpovědí (pokud LSS pořadí není dostupné)
            if not lss_ordered_responses:
                for subq in subquestions:
                    subq_df = df[df['question_code'] == subq]
                    all_responses.update(subq_df['response'].dropna().unique())
                lss_ordered_responses = sorted(list(all_responses))  # Alespoň abecední řazení

            # Vytvoření dat pro každou subotázku
            for subq in subquestions:
                subq_df = df[df['question_code'] == subq]
                subq_name = subq_df['question_name'].iloc[0] if not subq_df.empty else subq

                # Počty odpovědí pro tuto subotázku
                response_counts = subq_df['response'].value_counts()

                subq_data = {
                    'subquestion': subq_name,
                    'responses': {}
                }

                # OPRAVA: Použití LSS pořadí místo náhodného set()
                for response in lss_ordered_responses:
                    count = response_counts.get(response, 0)
                    if count > 0:  # Pouze odpovědi s nějakými daty
                        subq_data['responses'][response] = int(count)
                
                field_data.append(subq_data)
            
            # Základní informace o hlavní otázce pole
            question_info = {
                'code': main_code,
                'name': main_question_name,
                'type': 'array',  # Typ pole/matice
                'data': field_data,
                'subquestions': subquestions
            }
            
            chart_data.append(question_info)
        
        # Export do JSON
        import json
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(chart_data, f, ensure_ascii=False, indent=2)
            
        logger.info(f"Data pro grafy úspěšně vygenerována: {output_path}")
        return True
        
    except Exception as e:
        logger.error(f"Chyba při generování dat pro grafy: {str(e)}")
        return False

def _get_array_question_title_from_lss(question_code: str, long_path: str) -> str:
    """Získá skutečný název array otázky z LSS struktury"""
    try:
        # Odvodíme cestu k LSS souboru z long_path
        survey_dir = os.path.dirname(long_path)
        lss_path = os.path.join(survey_dir, "structure.lss")

        if not os.path.exists(lss_path):
            return f"Otázka pole: {question_code}"

        with open(lss_path, 'r', encoding='utf-8') as f:
            structure = json.load(f)

        # Hledáme otázku v LSS struktuře
        for group in structure.get('groups', []):
            for question in group.get('questions', []):
                if question.get('title') == question_code:
                    question_text = question.get('question', '')
                    # Odstranění HTML značek
                    if question_text:
                        question_text = strip_html(question_text)
                        return question_text
                    break

        return f"Otázka pole: {question_code}"

    except Exception as e:
        logger.error(f"Chyba při hledání názvu array otázky {question_code}: {str(e)}")
        return f"Otázka pole: {question_code}"

def validate_csv_structure(file_path: str) -> bool:
    """Validace struktury CSV souboru"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Validace CSV se středníkem jako oddělovačem
        reader = csv.reader(StringIO(content), delimiter=';', quotechar='"')
        headers = next(reader)
        row_count = 0
        invalid_rows = []
        expected_columns = len(headers)
        
        # Kontrola každého řádku a pole
        for i, row in enumerate(reader, 2):
            row_count += 1
            
            # Kontrola počtu sloupců
            if len(row) != expected_columns:
                invalid_rows.append(f"Řádek {i}: očekáváno {expected_columns} sloupců, nalezeno {len(row)}")
                continue
                
            # Kontrola textových polí
            for j, field in enumerate(row):
                # Kontrola neuzavřených uvozovek
                if field.count('"') % 2 != 0:
                    invalid_rows.append(f"Řádek {i}, sloupec {j+1}: neuzavřené uvozovky")
                    
                # Kontrola zalomení řádků
                if '\n' in field and not (field.startswith('"') and field.endswith('"')):
                    invalid_rows.append(f"Řádek {i}, sloupec {j+1}: nezabalené zalomení řádku")
                    
                # Kontrola escapovaných uvozovek - pouze pokud pole obsahuje uvozovky
                if field.startswith('"') and field.endswith('"') and len(field) > 2:
                    inner_content = field[1:-1]  # Obsah bez vnějších uvozovek
                    # Kontrola, že všechny uvozovky uvnitř jsou escapované (zdvojené)
                    # Nahradíme všechny "" za placeholder a zkontrolujeme, zda nezůstaly jednotlivé "
                    temp_content = inner_content.replace('""', '__ESCAPED_QUOTE__')
                    if '"' in temp_content:
                        invalid_rows.append(f"Řádek {i}, sloupec {j+1}: neescapované uvozovky v obsahu")
                
        if invalid_rows:
            logger.warning(f"Nalezeny chyby v CSV:\n" + "\n".join(invalid_rows))
            return False
                
        logger.info(f"Soubor {file_path} úspěšně validován ({row_count} řádků)")
        return True
        
    except Exception as e:
        logger.error(f"Chyba při validaci CSV: {str(e)}")
        return False

def _get_lss_answer_order(question_code: str, long_path: str) -> list:
    """Získá pořadí odpovědí z LSS struktury nebo použije známé vzory"""
    try:
        # OPRAVA: Nejprve zkusíme načíst skutečné pořadí z LSS struktury
        data_dir = os.path.dirname(long_path)
        lss_path = os.path.join(data_dir, "structure.lss")
        
        if os.path.exists(lss_path):
            try:
                with open(lss_path, 'r', encoding='utf-8') as f:
                    lss_structure = json.load(f)
                
                # Hledáme otázku v LSS struktuře
                for group in lss_structure.get('groups', []):
                    for question in group.get('questions', []):
                        if question.get('title') == question_code:
                            # Zkusíme najít answeroptions s order atributy
                            properties = question.get('properties', {})
                            answeroptions = properties.get('answeroptions', {})
                            
                            if answeroptions and isinstance(answeroptions, dict):
                                # Seřadíme podle order atributu
                                sorted_options = sorted(
                                    answeroptions.items(),
                                    key=lambda x: x[1].get('order', 999)
                                )
                                
                                # Extrahujeme answer texty v správném pořadí
                                ordered_responses = [opt[1]['answer'] for opt in sorted_options]
                                
                                logger.info(f"Použito LSS answeroptions pořadí pro {question_code}: {ordered_responses}")
                                return ordered_responses
                            
                            # Fallback na available_answers (pro multiple choice otázky)
                            available_answers = properties.get('available_answers', {})
                            if available_answers and isinstance(available_answers, dict):
                                # Pro available_answers zkusíme najít pořadí v subquestions
                                subquestions = properties.get('subquestions', {})
                                if subquestions and isinstance(subquestions, dict):
                                    # Seřadíme subquestions podle QID (což odpovídá pořadí v LSS)
                                    sorted_subqs = sorted(
                                        subquestions.items(),
                                        key=lambda x: int(x[0]) if x[0].isdigit() else 999
                                    )
                                    
                                    # Extrahujeme question texty v pořadí subquestions
                                    ordered_responses = [subq[1]['question'] for subq in sorted_subqs]
                                    logger.info(f"Použito subquestions pořadí pro {question_code}: {ordered_responses}")
                                    return ordered_responses
                                else:
                                    # Fallback na pořadí klíčů v available_answers
                                    ordered_responses = list(available_answers.values())
                                    logger.info(f"Použito available_answers pořadí pro {question_code}: {ordered_responses}")
                                    return ordered_responses
                            
                            break
                
                logger.warning(f"Otázka {question_code} nenalezena v LSS struktuře")
                
            except Exception as e:
                logger.warning(f"Chyba při čtení LSS struktury: {e}")
        
        # Fallback: Načteme data a zjistíme, jaké odpovědi skutečně existují
        responses_long_path = os.path.join(data_dir, "responses_long.csv")

        if os.path.exists(responses_long_path):
            import pandas as pd
            if pd is not None:
                df = pd.read_csv(responses_long_path)

                # Najdeme všechny odpovědi pro tuto otázku
                question_data = df[df['question_code'].str.startswith(question_code)]
                if not question_data.empty:
                    unique_responses = set(question_data['response'].dropna().unique())

                    # Známé škálové vzory v logickém pořadí
                    scale_patterns = [
                        # Český souhlas
                        ['rozhodně ano', 'spíše ano', 'spíše ne', 'rozhodně ne', 'neumím to posoudit'],
                        # Anglický souhlas
                        ['strongly agree', 'agree', 'disagree', 'strongly disagree', 'cannot assess'],
                        # Spokojenost
                        ['velmi spokojen', 'spokojen', 'nespokojen', 'velmi nespokojen', 'neumím posoudit'],
                        # Relevance
                        ['velmi relevantní', 'relevantní', 'irelevantní', 'velmi irelevantní', 'neumím posoudit'],
                        # Frekvence
                        ['vždy', 'skoro vždy', 've většině případů', 'v menšině případů', 'zřídka', 'nikdy', 'neumím posoudit']
                    ]

                    # Najdeme nejlepší vzor
                    for pattern in scale_patterns:
                        # Spočítáme, kolik odpovědí z vzoru se nachází v datech
                        matches = sum(1 for p in pattern if p in unique_responses)
                        if matches >= 3:  # Alespoň 3 odpovědi z vzoru
                            # Vrátíme pouze odpovědi, které skutečně existují v datech
                            ordered_responses = [p for p in pattern if p in unique_responses]
                            logger.info(f"Použit škálový vzor pro {question_code}: {ordered_responses}")
                            return ordered_responses

                    # Fallback - abecední řazení
                    logger.warning(f"Nerozpoznán škálový vzor pro {question_code}, použito abecední řazení")
                    return sorted(list(unique_responses))

        logger.warning(f"Nepodařilo se načíst data pro {question_code}")
        return []

    except Exception as e:
        logger.error(f"Chyba při získávání LSS pořadí: {e}")
        return []

def _get_logical_response_order(responses: list) -> list:
    """Získá logické pořadí odpovědí pro single/multiple choice otázky"""
    try:
        if not responses:
            return []

        # Převedeme na lowercase pro porovnání
        responses_lower = [str(r).lower() for r in responses]

        # Známé logické vzory (v pořadí od pozitivního k negativnímu)
        logical_patterns = [
            # Ano/Ne
            ['ano', 'ne'],
            ['yes', 'no'],
            # Souhlas
            ['souhlasím', 'nesouhlasím'],
            ['agree', 'disagree'],
            # Spokojenost
            ['spokojen', 'nespokojen'],
            ['satisfied', 'dissatisfied'],
            # Relevance
            ['relevantní', 'irelevantní'],
            ['relevant', 'irrelevant'],
            # Užitečnost
            ['užitečné', 'neužitečné'],
            ['useful', 'useless'],
        ]

        # Najdeme nejlepší vzor
        for pattern in logical_patterns:
            # Spočítáme, kolik odpovědí z vzoru se nachází v datech
            matches = []
            for p in pattern:
                for i, r in enumerate(responses_lower):
                    if p in r:
                        matches.append((i, responses[i]))  # (index, původní odpověď)
                        break

            if len(matches) >= 2:  # Alespoň 2 odpovědi z vzoru
                # Seřadíme podle vzoru a vrátíme původní odpovědi
                ordered = [match[1] for match in sorted(matches, key=lambda x: pattern.index(next(p for p in pattern if p in responses_lower[x[0]])))]

                # Přidáme zbývající odpovědi na konec
                remaining = [r for r in responses if r not in ordered]
                result = ordered + remaining

                logger.info(f"Použit logický vzor: {[r for r in result]}")
                return result

        # Fallback - původní pořadí (podle četnosti)
        logger.info(f"Nerozpoznán logický vzor, použito původní pořadí: {responses}")
        return responses

    except Exception as e:
        logger.error(f"Chyba při logickém řazení odpovědí: {e}")
        return responses
