
import os
from path_manager import path_manager
from app_initializer import app_initializer

# Globální proměnné - nyn<PERSON> spravované přes PathManager
current_survey_id = None
current_server_key = None

def check_server_and_survey_required():
    """
    <PERSON><PERSON><PERSON><PERSON>, zda jsou nastaveny server a průzkum

    Returns:
        bool: True pokud jsou nastaveny, False pokud ne
    """
    global current_server_key, current_survey_id

    if current_server_key is None and current_survey_id is None:
        print("❌ Nejprve musíte nastavit server a vybrat průzkum")
        print("💡 Použijte Menu 11 pro výběr serveru a průzkumu")
        return False
    elif current_survey_id is None:
        print("❌ Nejprve musíte vybrat průzkum")
        print("💡 Použijte Menu 11 pro výběr serveru a průzkumu")
        return False

    return True

def get_current_server_info():
    """
    Získá informace o aktuálním serveru

    Returns:
        dict: Informace o serveru
    """
    global current_server_key
    from config_loader import get_available_servers

    if current_server_key is None:
        return {
            'key': 'default',
            'name': 'dotazniky.urad.online',
            'url': 'default'
        }

    servers = get_available_servers()
    if current_server_key in servers:
        server = servers[current_server_key]
        return {
            'key': current_server_key,
            'name': server['name'],
            'url': server['API_URL']
        }

    return {
        'key': 'unknown',
        'name': 'Neznámý server',
        'url': 'unknown'
    }

def select_server_and_survey():
    """Nové Menu 11 - Výběr LimeSurvey serveru a průzkumu"""
    global current_server_key, current_survey_id
    from config_loader import get_available_servers
    from limesurvey_wrapper import create_limesurvey_client

    print("\n" + "=" * 60)
    print("=== VÝBĚR SERVERU A PRŮZKUMU ===")
    print("=" * 60)

    # Krok 1: Výběr serveru
    servers = get_available_servers()

    print("\n📡 Krok 1: Výběr LimeSurvey serveru")
    print("-" * 40)
    print("0. Default server (dotazniky.urad.online)")

    server_list = list(servers.items()) if servers else []
    for i, (key, server) in enumerate(server_list, 1):
        print(f"{i}. {server['name']} ({server['API_URL']})")

    try:
        choice = input(f"\nVyberte server (0-{len(server_list)}) nebo Enter pro default: ").strip()

        if not choice or choice == "0":
            selected_server_key = None
            server_name = "dotazniky.urad.online"
        else:
            choice_num = int(choice)
            if 1 <= choice_num <= len(server_list):
                selected_server_key = server_list[choice_num - 1][0]
                server_name = servers[selected_server_key]['name']
            else:
                print("❌ Neplatná volba")
                return

    except (ValueError, EOFError):
        print("❌ Výběr zrušen")
        return

    print(f"✅ Vybrán server: {server_name}")

    # Krok 2: Načtení a výběr průzkumu
    print(f"\n📋 Krok 2: Výběr průzkumu ze serveru {server_name}")
    print("-" * 40)

    try:
        print("🔍 Načítám seznam průzkumů...")
        client = create_limesurvey_client(selected_server_key)
        surveys = client.list_surveys()

        if not surveys:
            print("❌ Na tomto serveru nejsou žádné průzkumy")
            return

        print(f"\n📋 Dostupné průzkumy ({len(surveys)}):")
        print("=" * 60)

        for i, survey in enumerate(surveys, 1):
            survey_id = survey.get('sid', 'N/A')
            title = survey.get('surveyls_title', 'Bez názvu')[:50]
            active = "🟢 Aktivní" if survey.get('active') == 'Y' else "🔴 Neaktivní"

            print(f"{i:2d}. [{survey_id}] {title}")
            print(f"     {active}")
            print()

        print("=" * 60)

        # Výběr průzkumu
        choice = input(f"\nVyberte průzkum (1-{len(surveys)}) nebo Enter pro zrušení: ").strip()

        if not choice:
            print("❌ Výběr zrušen")
            return

        if choice.isdigit():
            choice_idx = int(choice) - 1
            if 0 <= choice_idx < len(surveys):
                selected_survey = surveys[choice_idx]
                survey_id = selected_survey.get('sid')
                title = selected_survey.get('surveyls_title', 'Bez názvu')

                # Nastavení globálních proměnných
                current_server_key = selected_server_key
                current_survey_id = str(survey_id)  # Zajistíme, že je to string

                # Synchronizace s PathManagerem
                path_manager.set_server(selected_server_key)
                path_manager.set_survey(survey_id)

                print("\n" + "=" * 60)
                print("✅ NASTAVENÍ DOKONČENO")
                print("=" * 60)
                print(f"🌐 Server: {server_name}")
                print(f"📊 Průzkum: [{survey_id}] {title}")
                print("=" * 60)
                print("💡 Nyní můžete používat ostatní menu pro práci s tímto průzkumem")

            else:
                print("❌ Neplatná volba")
        else:
            print("❌ Neplatná volba")

    except Exception as e:
        print(f"❌ Chyba při načítání průzkumů: {e}")

def select_server():
    """Starý výběr serveru - nyní přesměruje na nové menu"""
    print("⚠️  Tato funkce byla nahrazena novým Menu 11")
    print("💡 Použijte Menu 11 pro výběr serveru a průzkumu")
    return None

def display_menu():
    global current_survey_id, current_server_key
    print("=" * 60)
    print("=== LIMWRAPP - Hlavní menu ===")

    # Zobrazení aktuálního stavu
    server_info = get_current_server_info()

    if current_survey_id:
        print(f"📊 Aktuální průzkum: {current_survey_id}")
        print(f"🌐 Aktuální server: {server_info['name']}")
    else:
        print("📊 Aktuální průzkum: ❌ NENÍ NASTAVEN")
        print(f"🌐 Aktuální server: {server_info['name']}")
        print("⚠️  Pro použití menu 1-10, 12-16 musíte nejprve nastavit server a průzkum (Menu 11)")

    print("=" * 60)
    print("1. Vypsání seznamu průzkumů")
    print("2. Načtení dat průzkumu (CSV + LSS)")
    print("3. Načtení struktury průzkumu (LSS)")
    print("4. Propojení CSV a JSON souborů (mapování)")
    print("5. Transformace CSV dat na long formát")
    print("6. Generování datové struktury pro grafy")
    print("7. Generování grafů (původní)")
    print("8. 🆕 Generování grafů (rozšířené - všechny typy otázek)")
    print("9. 📥 Stažení PNG grafů")
    print("10. 🌍 Správa překladů a úprav názvů")
    print("11. 🔄 Výběr serveru a průzkumu")
    print("12. 📊 Datawrapper Export (HTML přehled grafů)")
    print("13. 🧠 Inteligentní analýza průzkumu")
    print("14. 🔧 Správa metadata Analysis Engine")
    print("15. 📊 Analytický Report Canvas (MVP)")
    print("16. 🔒 Ochrana soukromí - filtrování sloupců")
    print("17. 🔍 Diagnostika struktury průzkumu")

    # AI funkce - BEZPEČNÉ rozšíření
    try:
        from menu.ai_menu_functions import check_ai_availability
        if check_ai_availability():
            print("\n=== 🤖 AI Funkce ===")
            print("20. 🧠 AI WordCloud generování")
            print("21. ⚙️ Konfigurace typů grafů")
            print("22. 🔗 Správa virtuálních otázek")
            print("23. 📊 AI analýza grafů")
            print("24. 🔄 Reset nastavení grafů")
        else:
            print("\n💡 AI funkce dostupné po konfiguraci OPENAI_API_KEY")
    except ImportError:
        pass  # AI moduly nejsou dostupné
    except Exception:
        pass  # Tichá chyba - AI funkce nejsou kritické

    print("99. Zobrazení logu průzkumu")
    print("0. Ukončit program")
    print("=" * 60)

def list_surveys():
    """Získání seznamu průzkumů - nyní pouze informativní"""
    print("⚠️  Menu 1 bylo změněno")
    print("💡 Pro výběr serveru a průzkumu použijte Menu 11")
    print("📋 Toto menu nyní pouze zobrazuje průzkumy z aktuálně nastaveného serveru")

    # Kontrola nastavení
    if not check_server_and_survey_required():
        return None

    from limesurvey_wrapper import create_limesurvey_client
    global current_server_key

    try:
        client = create_limesurvey_client(current_server_key)
        surveys = client.list_surveys()

        if not surveys:
            print("❌ Na aktuálním serveru nejsou žádné průzkumy")
            return None

        server_info = get_current_server_info()
        print(f"\n📋 Průzkumy na serveru: {server_info['name']}")
        print("=" * 50)

        for i, survey in enumerate(surveys, 1):
            survey_id = survey.get('sid', 'N/A')
            title = survey.get('surveyls_title', 'Bez názvu')
            active = "🟢 Aktivní" if survey.get('active') == 'Y' else "🔴 Neaktivní"
            current_mark = "👈 AKTUÁLNÍ" if str(survey_id) == str(current_survey_id) else ""

            print(f"{i:2d}. [{survey_id}] {title}")
            print(f"     {active} {current_mark}")
            print()

        print("=" * 50)
        print("💡 Pro změnu průzkumu použijte Menu 11")
        return None

    except Exception as e:
        print(f"❌ Chyba při načítání průzkumů: {e}")
        return None

def get_survey_id():
    """Zastaralá funkce - nyní se používá globální current_survey_id"""
    print("⚠️  Tato funkce je zastaralá")
    print("💡 Použijte Menu 11 pro výběr serveru a průzkumu")
    return current_survey_id

def load_survey_data():
    """Načtení dat průzkumu"""
    # Kontrola nastavení
    if not check_server_and_survey_required():
        return

    from data_transformer import decompress_csv, validate_csv_structure
    global current_survey_id, current_server_key

    survey_id = current_survey_id
    server_info = get_current_server_info()

    print(f"📥 Načítám data průzkumu {survey_id}")
    print(f"🌐 Server: {server_info['name']}")
    print("=" * 50)

    try:
        from limesurvey_wrapper import create_limesurvey_client
        client = create_limesurvey_client(current_server_key)

        # Stažení odpovědí
        print("1/4 Stahuji odpovědi z LimeSurvey API...")
        file_path = client.get_responses(survey_id)
        print(f"✓ Odpovědi staženy do: {file_path}")

        # Stažení struktury
        print("2/4 Stahuji strukturu průzkumu...")
        structure_path = client.get_survey_structure(survey_id)
        print(f"✓ Struktura uložena do: {structure_path}")

        # Dekomprese
        print("3/4 Dekomprimuji CSV data...")
        decompress_csv(file_path)
        print("✓ Data dekomprimována")

        # Validace
        print("4/4 Validuji strukturu CSV...")
        if validate_csv_structure(file_path):
            print("✓ CSV struktura je v pořádku")
        else:
            print("⚠ CSV obsahuje varování - zkontrolujte log pro detaily")

        print("=" * 50)
        print(f"✅ Průzkum {survey_id} byl úspěšně načten a je připraven k dalšímu zpracování")

    except Exception as e:
        print("=" * 50)
        print(f"❌ Chyba při zpracování dat: {str(e)}")

def load_survey_structure():
    """Načtení struktury průzkumu"""
    # Kontrola nastavení
    if not check_server_and_survey_required():
        return

    global current_survey_id, current_server_key
    survey_id = current_survey_id
    server_info = get_current_server_info()

    print(f"📋 Načítám strukturu průzkumu {survey_id}")
    print(f"🌐 Server: {server_info['name']}")
    print("=" * 50)

    try:
        from limesurvey_wrapper import create_limesurvey_client
        client = create_limesurvey_client(current_server_key)
        lss_path = client.get_survey_structure(survey_id)
        print(f"✓ Struktura načtena a uložena do: {lss_path}")

        # Načtení a zobrazení struktury ze souboru
        import json
        with open(lss_path, 'r', encoding='utf-8') as f:
            structure = json.load(f)

        print(f"\nPřehled struktury průzkumu {survey_id}:")
        print(f"Počet skupin: {len(structure['groups'])}")

        total_questions = sum(len(group['questions']) for group in structure['groups'])
        print(f"Celkem otázek: {total_questions}")

        print("\nSkupiny otázek:")
        for i, group in enumerate(structure['groups'], 1):
            print(f"{i}. {group['group_name']} ({len(group['questions'])} otázek)")
            for j, question in enumerate(group['questions'][:3], 1):  # Zobraz jen první 3
                print(f"   {j}. {question['question'][:80]}{'...' if len(question['question']) > 80 else ''}")
            if len(group['questions']) > 3:
                print(f"   ... a dalších {len(group['questions']) - 3} otázek")

        print("=" * 50)
        print("✅ Struktura průzkumu byla úspěšně načtena")

    except Exception as e:
        print("=" * 50)
        print(f"❌ Chyba při načítání struktury: {str(e)}")

def merge_csv_json():
    """Propojení CSV a JSON souborů"""
    from data_transformer import generate_question_mapping
    import os
    
    # Kontrola nastavení
    if not check_server_and_survey_required():
        return

    global current_survey_id

    print(f"Vytvářím mapování otázek pro průzkum {current_survey_id}...")
    print("=" * 50)

    try:
        csv_path = path_manager.get_data_path(current_survey_id, "responses.csv")
        lss_path = path_manager.get_data_path(current_survey_id, "structure.lss")
        output_path = path_manager.get_data_path(current_survey_id, "question_mapping.csv")
        
        # Kontrola existence potřebných souborů
        if not os.path.exists(csv_path):
            print(f"✗ CSV soubor neexistuje: {csv_path}")
            print("Nejprve načtěte data průzkumu (volba 2)")
            return
            
        if not os.path.exists(lss_path):
            print(f"✗ LSS soubor neexistuje: {lss_path}")
            print("Nejprve načtěte strukturu průzkumu (volba 2 nebo 3)")
            return
        
        print(f"Načítám CSV data z: {csv_path}")
        print(f"Načítám strukturu z: {lss_path}")
        print("Generuji mapování otázek...")
        
        if generate_question_mapping(csv_path, lss_path, output_path, apply_privacy_filter=True):
            print("=" * 50)
            print(f"✓ Mapování otázek bylo úspěšně vygenerováno do: {output_path}")
        else:
            print("=" * 50)
            print("✗ Chyba při generování mapování otázek - zkontrolujte log pro detaily")
    except Exception as e:
        print("=" * 50)
        print(f"✗ Chyba při propojování souborů: {str(e)}")

def transform_to_long():
    """Transformace CSV dat na long formát"""
    from data_transformer import transform_to_long_format
    import os
    
    # Kontrola nastavení
    if not check_server_and_survey_required():
        return

    global current_survey_id

    print(f"Transformuji data průzkumu {current_survey_id} do long formátu...")
    
    try:
        csv_path = path_manager.get_data_path(current_survey_id, "responses.csv")
        mapping_path = path_manager.get_data_path(current_survey_id, "question_mapping.csv")
        output_path = path_manager.get_data_path(current_survey_id, "responses_long.csv")
        
        # Kontrola existence potřebných souborů
        if not os.path.exists(csv_path):
            print(f"Chyba: CSV soubor neexistuje: {csv_path}")
            print("Nejprve načtěte data průzkumu (volba 2)")
            return
            
        if not os.path.exists(mapping_path):
            print(f"Chyba: Mapovací soubor neexistuje: {mapping_path}")
            print("Nejprve vytvořte mapování otázek (volba 4)")
            return
        
        print(f"Načítám data z: {csv_path}")
        print(f"Používám mapování z: {mapping_path}")

        # Dotaz na filtrování dokončených záznamů
        print("\n" + "=" * 50)
        print("FILTROVÁNÍ ZÁZNAMŮ")
        print("=" * 50)
        print("Chcete zpracovat:")
        print("1. Všechny záznamy (včetně nedokončených)")
        print("2. Pouze dokončené záznamy")
        print("=" * 50)

        try:
            filter_choice = input("Vyberte volbu [2]: ") or "2"
        except EOFError:
            filter_choice = "2"

        filter_completed = (filter_choice == "2")

        if filter_completed:
            print("✓ Budou zpracovány pouze dokončené záznamy")
        else:
            print("⚠️  Budou zpracovány všechny záznamy (včetně nedokončených)")

        if transform_to_long_format(csv_path, mapping_path, output_path, filter_completed_only=filter_completed):
            if filter_completed:
                print(f"✓ Dokončené záznamy byly úspěšně transformovány: {output_path}")
            else:
                print(f"✓ Všechny záznamy byly úspěšně transformovány: {output_path}")
        else:
            print("✗ Chyba při transformaci dat")
            print("Poznámka: Pro transformaci dat je potřeba nainstalovat pandas knihovnu:")
            print("  sudo apt install python3-pandas")
            print("  nebo: pip install pandas")
    except Exception as e:
        print(f"✗ Chyba při transformaci dat: {str(e)}")

def generate_chart_structure():
    """Generování datové struktury pro grafy"""
    from data_transformer import generate_chart_data
    import os
    
    # Kontrola nastavení
    if not check_server_and_survey_required():
        return

    global current_survey_id

    print(f"Generuji datovou strukturu pro grafy průzkumu {current_survey_id}...")
    print("=" * 50)

    try:
        long_path = path_manager.get_data_path(current_survey_id, "responses_long.csv")
        output_path = path_manager.get_data_path(current_survey_id, "chart_data.json")
        
        # Kontrola existence long formátu
        if not os.path.exists(long_path):
            print(f"✗ Long formát neexistuje: {long_path}")
            print("Nejprve transformujte data do long formátu (volba 5)")
            return
        
        print(f"Načítám long formát z: {long_path}")
        print("Agreguji data pro grafy...")
        
        if generate_chart_data(long_path, output_path):
            print("=" * 50)
            print(f"✓ Data pro grafy byla úspěšně vygenerována: {output_path}")
        else:
            print("=" * 50)
            print("✗ Chyba při generování dat pro grafy")
            print("Poznámka: Pro generování dat je potřeba nainstalovat pandas knihovnu:")
            print("  sudo apt install python3-pandas")
            print("  nebo: pip install pandas")
    except Exception as e:
        print("=" * 50)
        print(f"✗ Chyba při generování dat pro grafy: {str(e)}")

def generate_charts_menu():
    """Generování grafů"""
    from chart_generator import ChartGenerator
    import os
    
    # Kontrola nastavení
    if not check_server_and_survey_required():
        return

    global current_survey_id

    print(f"Generuji grafy pro průzkum {current_survey_id}...")
    print("=" * 50)

    try:
        data_path = path_manager.get_data_path(current_survey_id, "chart_data.json")
        output_dir = path_manager.get_charts_path(current_survey_id) + "/"

        # Kontrola existence dat pro grafy
        if not os.path.exists(data_path):
            print(f"✗ Data pro grafy neexistují: {data_path}")
            print("Nejprve vygenerujte datovou strukturu pro grafy (volba 6)")
            return
        
        # Sběr metadat pro grafy
        print("Zadejte metadata pro grafy (můžete nechat prázdné):")
        try:
            survey_title = input("Název průzkumu (Description): ") or f"Průzkum {current_survey_id}"
            data_source = input("Zdroj dat (Data source): ") or ""
            data_source_link = input("Odkaz na zdroj dat (Link to data source): ") or ""
            byline = input("Autor/Byline: ") or ""
            
            print("\nNastavení PNG exportu:")
            print("ℹ️  Výška se vypočítá podle poměru stran publikovaného grafu")
            png_width = int(input("Cílová šířka PNG (px) [600]: ") or "600")
            png_border = int(input("Okraj/Border (px) [10]: ") or "10")
            png_scale = int(input("Zoom factor (násobič) [2]: ") or "2")
            full_header_input = input("Plná hlavička a patička? (y/n) [n]: ") or "n"
            full_header_footer = full_header_input.lower() in ['y', 'yes', 'ano']
            transparent_input = input("Transparentní pozadí? (y/n) [n]: ") or "n"
            transparent_bg = transparent_input.lower() in ['y', 'yes', 'ano']
            
            # Automatická výška podle poměru stran
            auto_height = True  # Vždy automatická výška (vypočítaná)
            
        except (EOFError, ValueError):
            # Fallback pro piped input nebo chybné hodnoty
            survey_title = f"Průzkum {current_survey_id}"
            data_source = ""
            data_source_link = ""
            byline = ""
            png_width = 600  # Cílová šířka
            png_border = 10
            png_scale = 2
            auto_height = True  # Vždy automatická výška (vypočítaná)
            full_header_footer = False
            transparent_bg = False
        
        print(f"Načítám data z: {data_path}")
        print("Generuji grafy...")
        
        generator = ChartGenerator(
            survey_title=survey_title,
            data_source=data_source,
            data_source_link=data_source_link,
            byline=byline,
            png_width=png_width,
            png_border=png_border,
            png_scale=png_scale,
            auto_height=auto_height,
            full_header_footer=full_header_footer,
            transparent_bg=transparent_bg
        )
        results = generator.generate_charts(data_path, output_dir, current_survey_id)
        
        if results:
            print("=" * 50)
            print(f"✓ Grafy byly úspěšně vygenerovány do: {output_dir}")
            print(f"Počet vygenerovaných grafů: {len(results)}")
        else:
            print("=" * 50)
            print("✗ Nepodařilo se vygenerovat žádné grafy")
            print("Možné příčiny:")
            print("1. Chybí pandas knihovna:")
            print("   sudo apt install python3-pandas")
            print("   nebo: pip install pandas")
            print("2. Chybí DatawrapperClient modul")
            print("3. Chybí konfigurace pro Datawrapper API")
            
    except Exception as e:
        print("=" * 50)
def generate_enhanced_charts_menu():
    """Rozšířené generování grafů pro všechny typy LimeSurvey otázek"""
    try:
        from enhanced_chart_generator import EnhancedChartGenerator
    except ImportError as e:
        print("=" * 50)
        print("✗ Chyba při importu rozšířených modulů:")
        print(f"  {str(e)}")
        print("Použijte původní generování grafů (volba 7)")
        return
    
    import os
    import json
    
    # Kontrola nastavení
    if not check_server_and_survey_required():
        return

    global current_survey_id

    print(f"🚀 Rozšířené generování grafů pro průzkum {current_survey_id}...")
    print("=" * 60)

    try:
        # Výběr jazyka pro generování grafů
        from translation_manager import TranslationManager
        tm = TranslationManager(current_survey_id)

        # Kontrola existence připravených dat
        base_chart_data_path = path_manager.get_data_path(current_survey_id, "chart_data.json")
        responses_long_path = path_manager.get_data_path(current_survey_id, "responses_long.csv")
        question_mapping_path = path_manager.get_data_path(current_survey_id, "question_mapping.csv")

        if not os.path.exists(base_chart_data_path):
            print(f"✗ Základní data pro grafy neexistují: {base_chart_data_path}")
            print("Nejprve vygenerujte datovou strukturu pro grafy (volba 6)")
            return

        # Získání dostupných jazykových verzí
        available_languages = tm.get_available_chart_languages(base_chart_data_path)

        if len(available_languages) == 1:
            # Pouze původní verze
            selected_language = 'original'
            chart_data_path = base_chart_data_path
            print(f"📊 Použita původní verze dat (bez překladů)")
        else:
            # Výběr jazyka
            print(f"\n🌍 Dostupné jazykové verze:")
            lang_options = list(available_languages.items())
            for i, (lang_code, lang_name) in enumerate(lang_options):
                print(f"   {i+1}. {lang_name} ({lang_code})")

            try:
                choice = input(f"\nVyberte jazyk (1-{len(lang_options)}) [1]: ") or "1"
                choice_idx = int(choice) - 1

                if 0 <= choice_idx < len(lang_options):
                    selected_language, selected_name = lang_options[choice_idx]
                    print(f"✅ Vybrán jazyk: {selected_name}")

                    if selected_language == 'original':
                        chart_data_path = base_chart_data_path
                    else:
                        chart_data_path = base_chart_data_path.replace('.json', f'_{selected_language}.json')
                else:
                    print("❌ Neplatná volba, použita původní verze")
                    selected_language = 'original'
                    chart_data_path = base_chart_data_path

            except (ValueError, EOFError):
                print("❌ Neplatná volba, použita původní verze")
                selected_language = 'original'
                chart_data_path = base_chart_data_path

        if not os.path.exists(chart_data_path):
            print(f"✗ Data pro vybraný jazyk neexistují: {chart_data_path}")
            if selected_language != 'original':
                print(f"Nejprve aplikujte překlady pro jazyk {selected_language} (Menu 10)")
            return
            
        if not os.path.exists(responses_long_path):
            print(f"✗ Long formát dat neexistuje: {responses_long_path}")
            print("Nejprve transformujte data do long formátu (volba 5)")
            return
            
        if not os.path.exists(question_mapping_path):
            print(f"✗ Mapování otázek neexistuje: {question_mapping_path}")
            print("Nejprve vytvořte mapování otázek (volba 4)")
            return
        
        # OPRAVA: Načtení a aplikace privacy filtru PŘED zobrazením
        print("✅ Kontroluji připravená data...")
        with open(chart_data_path, 'r', encoding='utf-8') as f:
            chart_data = json.load(f)

        # Aplikace privacy filtru
        original_count = len(chart_data)
        try:
            from privacy_filter import get_privacy_filter
            privacy_filter = get_privacy_filter(current_survey_id)

            if privacy_filter.excluded_columns:
                # Aplikujeme filtr stejně jako v Enhanced Chart Generator
                generator = EnhancedChartGenerator(language=chart_language)
                chart_data = generator._apply_privacy_filter_to_chart_data(chart_data, current_survey_id)
                excluded_count = original_count - len(chart_data)

                if excluded_count > 0:
                    print(f"🔒 Privacy filtr aplikován: vyloučeno {excluded_count} otázek")
        except Exception as e:
            print(f"⚠️  Privacy filtr se neaplikoval: {e}")

        print(f"📊 Nalezeno {len(chart_data)} připravených grafů")
        print(f"🔍 DEBUG: Používám soubor: {chart_data_path.split('/')[-1]}")
        print(f"🔍 DEBUG: Privacy filtr aktivní: {len(privacy_filter.excluded_columns) if 'privacy_filter' in locals() else 'N/A'} vyloučených sloupců")
        
        # Zobrazení všech grafů s čísly
        print(f"\n📈 Připravené grafy:")
        for i, chart in enumerate(chart_data):
            print(f"   {i+1}. {chart['name']} ({chart['code']}): {chart['type']}")

        # Potvrzení od uživatele s možností výběru
        try:
            confirm = input(f"\nChcete vygenerovat všech {len(chart_data)} grafů? (a/n): ")
        except EOFError:
            return

        selected_indices = None
        if confirm.lower() not in ['a', 'ano', 'y', 'yes']:
            # Uživatel nechce všechny grafy - umožníme výběr
            try:
                selection = input("Zadejte čísla grafů, které chcete vygenerovat (oddělená čárkami): ").strip()
                if not selection:
                    print("Generování zrušeno")
                    return

                # Parsování výběru
                selected_numbers = []
                for num_str in selection.split(','):
                    num_str = num_str.strip()
                    if num_str.isdigit():
                        num = int(num_str)
                        if 1 <= num <= len(chart_data):
                            selected_numbers.append(num - 1)  # Převod na 0-based index
                        else:
                            print(f"⚠️  Číslo {num} je mimo rozsah (1-{len(chart_data)})")
                    else:
                        print(f"⚠️  '{num_str}' není platné číslo")

                if not selected_numbers:
                    print("❌ Nebyly vybrány žádné platné grafy")
                    return

                selected_indices = selected_numbers
                print(f"✅ Vybráno {len(selected_indices)} grafů pro generování")

            except EOFError:
                print("Generování zrušeno")
                return
        
        # Sběr metadat pro grafy
        print("\n📝 Zadejte metadata pro grafy (můžete nechat prázdné):")
        try:
            survey_title = input("Název průzkumu: ") or f"Průzkum {current_survey_id}"
            data_source = input("Zdroj dat: ") or "LimeSurvey"
            data_source_link = input("Odkaz na zdroj: ") or ""
            byline = input("Autor: ") or ""

            print("\nNastavení PNG exportu:")
            print("ℹ️  Výška se vypočítá podle poměru stran publikovaného grafu")
            png_width = int(input("Cílová šířka PNG (px) [600]: ") or "600")
            png_border = int(input("Okraj/Border (px) [10]: ") or "10")
            png_scale = int(input("Zoom factor (násobič) [2]: ") or "2")
            full_header_input = input("Plná hlavička a patička? (y/n) [n]: ") or "n"
            full_header_footer = full_header_input.lower() in ['y', 'yes', 'ano']
            transparent_input = input("Transparentní pozadí? (y/n) [n]: ") or "n"
            transparent_bg = transparent_input.lower() in ['y', 'yes', 'ano']

            # Automatická výška podle poměru stran
            auto_height = True  # Vždy automatická výška (vypočítaná)

        except (EOFError, ValueError):
            # Fallback pro piped input nebo chybné hodnoty
            survey_title = f"Průzkum {current_survey_id}"
            data_source = "LimeSurvey"
            data_source_link = ""
            byline = ""
            png_width = 600  # Cílová šířka
            png_border = 10
            png_scale = 2
            auto_height = True  # Vždy automatická výška (vypočítaná)
            full_header_footer = False
            transparent_bg = False
        
        # OPRAVA: Nastavení jazyka podle výběru s extrakcí správného kódu
        if selected_language == 'original':
            chart_language = "cs-CZ"  # Původní je česky
        else:
            # Extrakce správného jazykového kódu z názvu souboru
            # ai_improved_cs-CZ → cs-CZ, test_en-US → en-US
            if '_' in selected_language:
                # Najdeme poslední část po podtržítku
                parts = selected_language.split('_')
                last_part = parts[-1]
                # Zkontrolujeme, zda je to platný jazykový kód (xx-XX)
                if '-' in last_part and len(last_part) == 5:
                    chart_language = last_part
                else:
                    chart_language = "cs-CZ"  # Fallback
            else:
                chart_language = selected_language

        print(f"🌍 Jazyk grafů: {available_languages.get(selected_language, selected_language)}")

        # Vytvoření enhanced chart generatoru
        generator = EnhancedChartGenerator(
            survey_title=survey_title,
            data_source=data_source,
            data_source_link=data_source_link,
            byline=byline,
            png_width=png_width,
            png_border=png_border,
            png_scale=png_scale,
            auto_height=auto_height,
            full_header_footer=full_header_footer,
            transparent_bg=transparent_bg,
            language=chart_language
        )
        
        # Příprava dat pro generování
        if selected_indices is not None:
            # Filtrování pouze vybraných grafů
            selected_chart_data = [chart_data[i] for i in selected_indices]
            selected_question_ids = [chart['code'] for chart in selected_chart_data]

            print(f"\n🎯 Generuji {len(selected_chart_data)} vybraných grafů:")
            for i, chart in enumerate(selected_chart_data):
                print(f"   {i+1}. {chart['name']} ({chart['code']})")
        else:
            # Všechny grafy
            selected_question_ids = None
            print(f"\n🎨 Generuji všech {len(chart_data)} grafů z připravených dat...")

        # Generování grafů z připravených dat
        output_dir = f"charts/{current_survey_id}"
        print("=" * 50)

        results = generator.generate_charts_from_prepared_data(
            chart_data_path=chart_data_path,
            responses_long_path=responses_long_path,
            question_mapping_path=question_mapping_path,
            output_base="charts",
            survey_id=current_survey_id,
            question_ids=selected_question_ids  # Vybrané otázky nebo None pro všechny
        )
        
        # Vyhodnocení výsledků
        if results:
            successful = [r for r in results if r.get('status') == 'success']
            failed = [r for r in results if r.get('status') != 'success']
            
            print("=" * 50)
            print(f"✅ DOKONČENO!")
            print(f"📊 Úspěšně vygenerováno: {len(successful)} grafů")
            print(f"❌ Selhalo: {len(failed)} grafů")
            print(f"📁 Výstupní složka: {output_dir}")
            
            if successful:
                print(f"\n📈 Úspěšné grafy:")
                for result in successful[:5]:  # Zobrazíme prvních 5
                    print(f"   ✅ {result['question_title']}: {result['chart_type']}")
                    print(f"      📄 {result['png_path']}")
                    if result.get('chart_url'):
                        print(f"      🌐 {result['chart_url']}")
                
                if len(successful) > 5:
                    print(f"   ... a dalších {len(successful) - 5} grafů")
            
            if failed:
                print(f"\n❌ Selhané grafy:")
                for result in failed[:3]:  # Zobrazíme první 3 chyby
                    print(f"   ❌ {result.get('question_id', 'unknown')}: {result.get('error', 'Neznámá chyba')}")
        else:
            print("=" * 50)
            print("❌ Nepodařilo se vygenerovat žádné grafy")
            print("Možné příčiny:")
            print("1. Chybí pandas knihovna: pip install pandas")
            print("2. Chybí konfigurace pro Datawrapper API")
            print("3. Problémy s daty v responses.csv")
            
    except Exception as e:
        print("=" * 50)
        print(f"✗ Chyba při rozšířeném generování grafů: {str(e)}")
        print("Zkuste použít původní generování grafů (volba 7)")
        print(f"✗ Chyba při generování grafů: {str(e)}")

def download_png_charts():
    """Stažení PNG grafů z Datawrapper"""
    global current_survey_id, current_server_key

    # Kontrola nastavení
    if not check_server_and_survey_required():
        return

    try:
        from datawrapper_client import DatawrapperClient
        from config_loader import load_config
        import requests
        import json
        import re
        import os

        print("📥 Stažení PNG grafů z Datawrapper")
        print("=" * 50)
        print(f"Průzkum: {current_survey_id}")

        # Použití aktuálního survey_id
        survey_id = current_survey_id

        # Načtení konfigurace a použití path_manager
        config = load_config()

        # Použití path_manager pro správnou cestu
        charts_dir = path_manager.get_charts_path(survey_id)

        # Zajištění existence adresáře
        path_manager.ensure_directories(survey_id, current_server_key)
        print(f"📁 Cílový adresář: {charts_dir}")

        # Sběr parametrů pro PNG export
        print("\n📝 Nastavení PNG exportu:")
        try:
            png_width = int(input("Cílová šířka PNG (px) [600]: ") or "600")
            png_border = int(input("Okraj/Border (px) [10]: ") or "10")
            png_scale = int(input("Zoom factor (násobič) [2]: ") or "2")
            full_header_input = input("Plná hlavička a patička? (y/n) [n]: ") or "n"
            full_header_footer = full_header_input.lower() in ['y', 'yes', 'ano']
            transparent_input = input("Transparentní pozadí? (y/n) [n]: ") or "n"
            transparent_bg = transparent_input.lower() in ['y', 'yes', 'ano']
        except (EOFError, ValueError):
            png_width = 600
            png_border = 10
            png_scale = 2
            full_header_footer = False
            transparent_bg = False

        # Načtení Datawrapper konfigurace
        team_id = config.get('DATAWRAPPER_TEAM_ID')
        parent_folder_id = config.get('DATAWRAPPER_LIMESURVEY_FOLDER_ID')

        if not team_id or not parent_folder_id:
            print("❌ Chybí konfigurace DATAWRAPPER_TEAM_ID nebo DATAWRAPPER_LIMESURVEY_FOLDER_ID v .env")
            return

        # Inicializace Datawrapper klientů
        dw = DatawrapperClient()

        # Použití DatawrapperExportClient pro hledání složky
        from datawrapper_export import DatawrapperExportClient
        export_client = DatawrapperExportClient()

        # Hledání složky pro průzkum
        print(f"\n🔍 Hledání složky pro průzkum {survey_id}...")
        print(f"   Team ID: {team_id}")
        print(f"   Nadřazená složka: {parent_folder_id}")

        survey_folder_id = export_client.find_survey_folder(survey_id)

        if not survey_folder_id:
            print(f"❌ Složka pro průzkum {survey_id} nebyla nalezena")
            print("💡 Zkontrolujte, zda složka existuje v Datawrapper")
            return

        print(f"✅ Nalezena složka pro průzkum: {survey_folder_id}")

        # Načtení grafů ze složky
        charts = export_client.get_charts_in_folder(survey_folder_id)

        if not charts:
            print(f"❌ Ve složce {survey_folder_id} nejsou žádné grafy")
            return

        print(f"📊 Nalezeno {len(charts)} grafů ve složce")

        # Zobrazení prvních 5 grafů pro kontrolu
        print("\n📋 Ukázka grafů:")
        for i, chart in enumerate(charts[:5], 1):
            print(f"   {i}. {chart.title} (ID: {chart.id})")

        if len(charts) > 5:
            print(f"   ... a dalších {len(charts) - 5} grafů")

        # Stažení grafů
        downloaded = 0
        failed = 0

        for i, chart in enumerate(charts, 1):
            chart_id = chart.id
            chart_title = chart.title or f'Graf_{chart_id}'

            print(f"\n📊 [{i}/{len(charts)}] Stahuji: {chart_title}")

            try:
                # Export PNG
                png_data = dw.export_chart(
                    chart_id=chart_id,
                    export_format='png',
                    target_width=png_width,
                    border_width=png_border,
                    zoom=png_scale,
                    plain=not full_header_footer
                )

                if png_data:
                    # Vytvoření bezpečného názvu souboru
                    safe_title = re.sub(r'[<>:"/\\|?*]', '_', chart_title)
                    if len(safe_title) > 100:
                        safe_title = safe_title[:100] + "..."

                    png_filename = f"{chart_id}_{safe_title}.png"
                    png_path = os.path.join(charts_dir, png_filename)

                    # Uložení PNG
                    with open(png_path, 'wb') as f:
                        f.write(png_data)

                    print(f"   ✅ Uloženo: {png_filename}")
                    downloaded += 1
                else:
                    print(f"   ❌ Nepodařilo se exportovat graf {chart_id}")
                    failed += 1

            except Exception as e:
                print(f"   ❌ Chyba při stahování {chart_id}: {str(e)}")
                failed += 1

        # Shrnutí
        print(f"\n📊 Shrnutí stahování:")
        print(f"   ✅ Úspěšně staženo: {downloaded} grafů")
        print(f"   ❌ Selhalo: {failed} grafů")
        print(f"   📁 Adresář: {charts_dir}")

        if downloaded > 0:
            print(f"\n🎉 Grafy byly úspěšně staženy do složky: {charts_dir}")

    except ImportError:
        print("❌ Chyba: Datawrapper klient není dostupný")
    except Exception as e:
        print(f"❌ Chyba při stahování grafů: {str(e)}")

def translation_management_menu():
    """Menu pro správu překladů a úprav názvů"""
    # Kontrola nastavení
    if not check_server_and_survey_required():
        return

    global current_survey_id

    try:
        from translation_manager import TranslationManager
        import json

        tm = TranslationManager(current_survey_id)
        chart_data_path = path_manager.get_data_path(current_survey_id, "chart_data.json")

        if not os.path.exists(chart_data_path):
            print(f"Soubor {chart_data_path} neexistuje")
            print("Nejprve vygenerujte datovou strukturu pro grafy (Menu 6)")
            return

        while True:
            print("\n" + "=" * 60)
            print("=== SPRÁVA PŘEKLADŮ A ÚPRAV NÁZVŮ ===")
            print(f"Průzkum: {current_survey_id}")

            # Zobrazení statistik
            stats = tm.get_translation_stats()
            current_language = tm.get_chart_language()
            available_languages = tm.get_available_languages()

            print(f"Aktuální jazyk grafů: {available_languages.get(current_language, current_language)}")
            print(f"Statistiky překladů:")
            for category, data in stats.items():
                print(f"  {category}: {data['translated']}/{data['total']} přeloženo")

            print("=" * 60)
            print("1. 📝 Vygenerovat šablonu pro překlad")
            print("2. ✏️  Editovat překlady (otevřít soubor)")
            print("3. ✅ Aplikovat překlady na chart_data.json")
            print("4. 👁️  Zobrazit extrahované řetězce")
            print("5. 🌍 Nastavit jazyk grafů")
            print("6. 📁 Správa jazykových verzí")
            print("7. 🤖 AI úprava názvů pro grafy")
            print("0. Zpět do hlavního menu")
            print("=" * 60)

            try:
                choice = input("Vyberte volbu: ")
            except EOFError:
                break

            if choice == "0":
                break
            elif choice == "1":
                # Generování šablony s výběrem jazyka
                available_languages = tm.get_available_languages()
                existing_files = tm.get_available_translation_files()

                print("\n📝 Generování šablony pro překlad")
                print("=" * 40)

                # Zobrazení existujících souborů
                if existing_files:
                    print("Existující soubory překladů:")
                    for lang, filepath in existing_files.items():
                        if lang == 'legacy':
                            print(f"  - translations.json (starý formát)")
                        else:
                            lang_name = available_languages.get(lang, lang)
                            print(f"  - {lang} ({lang_name})")

                print("\nDostupné jazyky:")
                lang_list = list(available_languages.items())
                for i, (code, name) in enumerate(lang_list, 1):
                    exists_marker = " ✓" if code in existing_files else ""
                    print(f"{i}. {name} ({code}){exists_marker}")

                try:
                    choice_input = input(f"\nVyberte jazyk pro šablonu [1-{len(lang_list)}]: ")
                    if choice_input.isdigit():
                        choice_idx = int(choice_input) - 1
                        if 0 <= choice_idx < len(lang_list):
                            target_language = lang_list[choice_idx][0]
                            target_file = tm._get_translation_file_path(target_language)

                            # Kontrola existence
                            overwrite = False
                            if os.path.exists(target_file):
                                print(f"\n⚠️  Soubor {os.path.basename(target_file)} už existuje!")
                                overwrite_choice = input("Přepsat? (y/n): ").lower()
                                if overwrite_choice not in ['y', 'yes', 'ano']:
                                    print("Generování zrušeno")
                                    continue
                                overwrite = True

                            print(f"Generuji šablonu pro {available_languages[target_language]}...")
                            if tm.create_translation_template_for_language(chart_data_path, target_language, overwrite):
                                print(f"✓ Šablona vygenerována: {target_file}")
                                print("Můžete nyní editovat soubor a upravit překlady")
                            else:
                                print("✗ Chyba při generování šablony")
                        else:
                            print("Neplatná volba")
                    else:
                        print("Neplatná volba")
                except EOFError:
                    pass

            elif choice == "2":
                print(f"Otevírám soubor: {tm.translation_file}")
                print("Editujte soubor v textovém editoru a uložte změny")
                print("Formát: \"původní text\": \"přeložený text\"")
                input("Stiskněte Enter po dokončení editace...")

            elif choice == "3":
                # Aplikace překladů s výběrem jazykového souboru
                existing_files = tm.get_available_translation_files()
                available_languages = tm.get_available_languages()

                if not existing_files:
                    print("✗ Žádné soubory překladů neexistují")
                    print("Nejprve vygenerujte šablonu (volba 1)")
                    continue

                print("\n✅ Aplikace překladů")
                print("=" * 30)
                print("Dostupné soubory překladů:")

                file_list = []
                for lang, filepath in existing_files.items():
                    if lang == 'legacy':
                        file_list.append(('legacy', 'translations.json (starý formát)', filepath))
                    else:
                        lang_name = available_languages.get(lang, lang)
                        file_list.append((lang, f"{lang_name} ({lang})", filepath))

                for i, (lang_code, display_name, filepath) in enumerate(file_list, 1):
                    print(f"{i}. {display_name}")

                try:
                    file_choice = input(f"\nVyberte soubor překladů [1-{len(file_list)}]: ")
                    if file_choice.isdigit():
                        file_idx = int(file_choice) - 1
                        if 0 <= file_idx < len(file_list):
                            selected_lang, selected_name, selected_file = file_list[file_idx]

                            print(f"\nPoužiji překlady: {selected_name}")

                            # Nová logika: vždy vytvoř jazykový soubor chart_data_{jazyk}.json
                            print(f"Vytvoří se soubor: chart_data_{selected_lang}.json")

                            # Aplikace překladů - vždy vytvoří jazykový soubor
                            if selected_lang == 'legacy':
                                # Starý formát - použij původní metodu s explicitním výstupem
                                output_path = path_manager.get_data_path(current_survey_id, "chart_data_translated.json")
                                tm.translations = tm._load_translations()
                                result = tm.apply_translations(chart_data_path, output_path)
                                success_message = f"✓ Přeložená data uložena: {output_path}"
                            else:
                                # Nový formát - vytvoř chart_data_{jazyk}.json
                                result = tm.apply_translations_from_language(chart_data_path, selected_lang)
                                output_path = path_manager.get_data_path(current_survey_id, f"chart_data_{selected_lang}.json")
                                success_message = f"✓ Přeložená data uložena: {output_path}"

                            if result:
                                print(success_message)
                                print("Pro generování grafů s překlady použijte přeložený soubor")
                            else:
                                print("✗ Chyba při aplikaci překladů")
                        else:
                            print("Neplatná volba")
                    else:
                        print("Neplatná volba")
                except EOFError:
                    pass

            elif choice == "4":
                strings = tm.extract_translatable_strings(chart_data_path)
                print("\nExtrahované řetězce:")
                for category, items in strings.items():
                    print(f"\n{category.upper()}:")
                    for item in items[:10]:  # Zobraz jen prvních 10
                        print(f"  - {item}")
                    if len(items) > 10:
                        print(f"  ... a dalších {len(items) - 10} položek")

            elif choice == "5":
                # Nastavení jazyka grafů
                available_languages = tm.get_available_languages()
                current_language = tm.get_chart_language()

                print(f"\nAktuální jazyk: {available_languages.get(current_language, current_language)}")
                print("\nDostupné jazyky:")

                lang_list = list(available_languages.items())
                for i, (code, name) in enumerate(lang_list, 1):
                    marker = " ← aktuální" if code == current_language else ""
                    print(f"{i}. {name} ({code}){marker}")

                try:
                    choice_input = input(f"\nVyberte jazyk [1-{len(lang_list)}]: ")
                    if choice_input.isdigit():
                        choice_idx = int(choice_input) - 1
                        if 0 <= choice_idx < len(lang_list):
                            new_language = lang_list[choice_idx][0]
                            if tm.set_chart_language(new_language):
                                print(f"✓ Jazyk nastaven na: {available_languages[new_language]}")
                            else:
                                print("✗ Chyba při nastavování jazyka")
                        else:
                            print("Neplatná volba")
                    else:
                        print("Neplatná volba")
                except EOFError:
                    pass

            elif choice == "6":
                # Správa jazykových verzí
                existing_files = tm.get_available_translation_files()
                available_languages = tm.get_available_languages()

                print("\n📁 Správa jazykových verzí")
                print("=" * 40)

                if existing_files:
                    print("Existující soubory překladů:")
                    for lang, filepath in existing_files.items():
                        if lang == 'legacy':
                            print(f"  - translations.json (starý formát)")
                        else:
                            lang_name = available_languages.get(lang, lang)
                            file_size = os.path.getsize(filepath) if os.path.exists(filepath) else 0
                            print(f"  - {lang_name} ({lang}) - {file_size} bytů")
                else:
                    print("Žádné soubory překladů neexistují")

                print("\nVolby:")
                print("1. Zobrazit obsah souboru")
                print("2. Smazat soubor")
                print("3. Zkopírovat soubor do jiného jazyka")
                print("0. Zpět")

                try:
                    mgmt_choice = input("Vyberte volbu: ")

                    if mgmt_choice == "1" and existing_files:
                        # Zobrazit obsah
                        file_list = list(existing_files.items())
                        for i, (lang, filepath) in enumerate(file_list, 1):
                            display_name = "starý formát" if lang == 'legacy' else available_languages.get(lang, lang)
                            print(f"{i}. {display_name}")

                        file_choice = input(f"Vyberte soubor [1-{len(file_list)}]: ")
                        if file_choice.isdigit():
                            file_idx = int(file_choice) - 1
                            if 0 <= file_idx < len(file_list):
                                selected_lang, selected_file = file_list[file_idx]
                                print(f"\nObsah souboru {os.path.basename(selected_file)}:")
                                print("-" * 50)
                                try:
                                    with open(selected_file, 'r', encoding='utf-8') as f:
                                        content = json.load(f)
                                    print(json.dumps(content, ensure_ascii=False, indent=2)[:1000] + "...")
                                except Exception as e:
                                    print(f"Chyba při čtení souboru: {e}")

                    elif mgmt_choice == "2" and existing_files:
                        # Smazat soubor
                        print("⚠️  POZOR: Tato akce je nevratná!")
                        confirm = input("Opravdu chcete smazat soubor? (yes/ne): ")
                        if confirm.lower() == 'yes':
                            # Implementace mazání...
                            print("Mazání souborů není implementováno pro bezpečnost")

                    elif mgmt_choice == "3" and existing_files:
                        # Zkopírovat soubor
                        print("Kopírování mezi jazyky není zatím implementováno")

                except EOFError:
                    pass

            elif choice == "7":
                # AI úprava názvů pro grafy
                ai_improve_names_menu(tm, chart_data_path)

            else:
                print("Neplatná volba")

    except ImportError:
        print("Chyba: Nepodařilo se načíst TranslationManager")
    except Exception as e:
        print(f"Chyba: {str(e)}")

def display_log():
    """Zobrazení logu průzkumu"""
    from logger import get_logger
    try:
        logger = get_logger('limwrapp')
        if not logger.handlers:
            print("Žádné logy k zobrazení")
            return
            
        print("\nLogy průzkumu:")
        for handler in logger.handlers:
            if hasattr(handler, 'logs'):
                for log in handler.logs:
                    print(f"- {log}")
    except Exception as e:
        print(f"Chyba při načítání logů: {str(e)}")

def datawrapper_export_menu():
    """Menu pro Datawrapper Export - HTML přehled grafů"""
    global current_survey_id

    # Kontrola nastavení
    if not check_server_and_survey_required():
        return

    try:
        from datawrapper_export import ExportManager, ExportProgress

        print("\n" + "=" * 60)
        print("=== 📊 DATAWRAPPER EXPORT ===")
        print(f"Průzkum: {current_survey_id}")
        print("=" * 60)

        # Progress callback
        def progress_callback(progress: ExportProgress):
            percentage = progress.progress_percentage
            charts_info = ""
            if progress.total_charts > 0:
                charts_info = f" | Grafy: {progress.charts_processed}/{progress.total_charts}"
            print(f"🔄 {progress.current_step} ({percentage:.1f}%){charts_info}")

        # Inicializace Export Manager
        export_manager = ExportManager(progress_callback=progress_callback)

        # Test připojení
        print("🔍 Testuji připojení k Datawrapper API...")
        connection_test = export_manager.test_connection()

        if not connection_test['success']:
            print(f"❌ Chyba připojení k Datawrapper API: {connection_test.get('error', 'Neznámá chyba')}")
            print("💡 Zkontrolujte DATAWRAPPER_API_KEY v .env souboru")
            return

        print(f"✅ Připojení úspěšné (nalezeno {connection_test['folders_count']} složek)")

        # Volitelné parametry
        print("\n📋 Konfigurace exportu:")

        # Název průzkumu
        survey_name = input(f"Název průzkumu (Enter = {current_survey_id}): ").strip()
        if not survey_name:
            survey_name = current_survey_id

        # Filtr jazyka
        print("\nFiltr podle jazyka:")
        print("1. Všechny jazyky")
        print("2. Pouze české grafy")
        print("3. Pouze anglické grafy")

        try:
            lang_choice = input("Vyberte volbu (Enter = 1): ").strip()
            if not lang_choice:
                lang_choice = "1"
        except EOFError:
            lang_choice = "1"

        language_filter = None
        if lang_choice == "2":
            language_filter = "cs"
        elif lang_choice == "3":
            language_filter = "en"

        # Výstupní složka - použití path_manager
        default_output = path_manager.get_charts_path(current_survey_id)
        output_dir = input(f"Výstupní složka (Enter = {default_output}): ").strip()
        if not output_dir:
            output_dir = default_output

        # Zajištění existence výstupního adresáře
        path_manager.ensure_directories(current_survey_id, current_server_key)

        # Force reconfigure
        print("\nRekonfigurace grafů:")
        print("1. Ano - aktualizovat metadata všech grafů")
        print("2. Ne - použít stávající nastavení")

        try:
            reconfig_choice = input("Vyberte volbu (Enter = 1): ").strip()
            if not reconfig_choice:
                reconfig_choice = "1"
        except EOFError:
            reconfig_choice = "1"

        force_reconfigure = reconfig_choice == "1"

        # Spuštění exportu
        print(f"\n🚀 Spouštím export grafů pro průzkum {current_survey_id}...")
        print("=" * 60)

        result = export_manager.export_survey_charts(
            survey_id=current_survey_id,
            output_dir=output_dir,
            survey_name=survey_name,
            language_filter=language_filter,
            force_reconfigure=force_reconfigure
        )

        # Zobrazení výsledků
        print("\n" + "=" * 60)
        if result.success:
            print("✅ EXPORT ÚSPĚŠNĚ DOKONČEN")
            print(f"📁 Výstupní soubor: {result.output_file}")
            print(f"📊 Exportováno grafů: {result.charts_exported}")
            print(f"⏱️ Doba zpracování: {result.execution_time:.1f}s")

            # Detailní souhrn
            summary = export_manager.get_export_summary(result)

            if 'file_size' in summary:
                print(f"📏 Velikost souboru: {summary['file_size']}")

            if 'collection_stats' in summary:
                stats = summary['collection_stats']
                print(f"🇨🇿 České grafy: {stats['czech_charts']}")
                print(f"🇬🇧 Anglické grafy: {stats['english_charts']}")
                if stats['other_language_charts'] > 0:
                    print(f"🌍 Ostatní jazyky: {stats['other_language_charts']}")

            if 'configuration_stats' in summary:
                config_stats = summary['configuration_stats']
                print(f"⚙️ Konfigurace: {config_stats['successful']}/{result.charts_exported} úspěšných ({config_stats['success_rate']})")

        else:
            print("❌ EXPORT SELHAL")
            if result.errors:
                print("Chyby:")
                for error in result.errors:
                    print(f"  - {error}")

        print("=" * 60)
        input("Stiskněte Enter pro pokračování...")

    except ImportError as e:
        print("❌ Datawrapper export modul není dostupný")
        print(f"Chyba: {e}")
        print("💡 Zkontrolujte instalaci závislostí")
    except Exception as e:
        print(f"❌ Neočekávaná chyba: {str(e)}")
        import traceback
        traceback.print_exc()

def intelligent_analysis_menu():
    """Menu 13 - Inteligentní analýza průzkumu"""
    global current_survey_id, current_server_key
    
    try:
        from intelligent_analysis_hierarchical import HierarchicalAnalysisManager
        from intelligent_analysis_html_hierarchical import HierarchicalAnalysisHTMLGenerator

        print("\n" + "=" * 60)
        print("=== 🧠 INTELIGENTNÍ ANALÝZA PRŮZKUMU ===")
        print("Analysis Engine v2.0 - Hierarchická analýza")
        print("=" * 60)

        # Kontrola nastavení
        if not check_server_and_survey_required():
            return

        # Zobrazení aktuálního stavu
        state = path_manager.get_current_state()
        print(f"📊 Aktuální průzkum: {current_survey_id}")
        if state['server_name']:
            print(f"🌐 Server: {state['server_name']}")
        else:
            print("🌐 Server: dotazniky.urad.online (default)")

        # Kontrola existence potřebných souborů
        lss_file_path = path_manager.get_data_path(current_survey_id, "structure.lss")
        csv_file_path = path_manager.get_data_path(current_survey_id, "responses.csv")

        print(f"\n📁 Kontrola vstupních souborů:")
        print(f"   LSS struktura: {lss_file_path}")
        print(f"   CSV odpovědi: {csv_file_path}")

        if not os.path.exists(lss_file_path):
            print("❌ LSS soubor neexistuje")
            print("💡 Spusťte Menu 2 nebo 3 pro načtení struktury průzkumu")
            return

        csv_exists = os.path.exists(csv_file_path)
        if csv_exists:
            print("✅ Oba soubory jsou k dispozici - plná analýza možná")
        else:
            print("⚠️  CSV soubor neexistuje - pouze strukturální analýza")
            print("💡 Pro plnou analýzu spusťte Menu 2 pro načtení dat")

        # Inicializace manageru
        print("\n🔧 Inicializuji Analysis Engine...")
        manager = HierarchicalAnalysisManager()

        # Spuštění hierarchické analýzy
        print(f"\n🚀 Spouštím hierarchickou analýzu průzkumu {current_survey_id}...")
        print("=" * 60)

        result = manager.analyze_survey_hierarchical(lss_file_path, csv_file_path if csv_exists else None)

        if not result:
            print("❌ Analýza selhala")
            return

        # Zobrazení výsledků
        summary = result['summary']
        print("\n✅ HIERARCHICKÁ ANALÝZA DOKONČENA")
        print("=" * 60)
        print(f"📊 Celkem skupin: {summary.get('total_groups', 0)}")
        print(f"📋 Celkem otázek: {summary['total_questions']}")
        print(f"🎯 Rozpoznáno: {summary['recognized_questions']} ({summary['recognition_rate']:.1%})")
        print(f"📈 Doporučené analýzy: {summary['total_analyses']}")
        print(f"🎨 Vizualizace: {summary['total_visualizations']}")
        print(f"⏱️ Odhadovaný čas: {summary['estimated_time_hours']:.1f} hodin")
        print(f"🔧 Složitost: {summary['complexity_score']:.1f}/10")
        print(f"📊 Datawrapper kompatibilita: {summary['datawrapper_compatibility']:.1%}")
        print(f"🚀 Priorita implementace: {summary['implementation_priority']}")

        # Zobrazení hierarchické struktury (zkrácené)
        if 'hierarchical_structure' in result:
            print(f"\n📋 Struktura průzkumu:")
            structure = result['hierarchical_structure']
            for i, group in enumerate(structure.get('groups', [])[:3], 1):  # Zobraz jen první 3 skupiny
                print(f"   {i}. {group['group_name']} ({group['question_count']} otázek)")
                for j, question in enumerate(group['questions'][:2], 1):  # Zobraz jen první 2 otázky
                    q_text = question['question_text'][:60] + "..." if len(question['question_text']) > 60 else question['question_text']
                    print(f"      {j}. {q_text}")
                if len(group['questions']) > 2:
                    print(f"      ... a dalších {len(group['questions']) - 2} otázek")
            
            if len(structure.get('groups', [])) > 3:
                print(f"   ... a dalších {len(structure['groups']) - 3} skupin")

        # Export výsledků
        print("\n📤 Export výsledků:")
        print("1. JSON report")
        print("2. HTML report (hierarchický)")
        print("3. Oba formáty")
        print("4. Přeskočit export")
        
        try:
            export_choice = input("Vyberte volbu (Enter = 3): ").strip()
            if not export_choice:
                export_choice = "3"
        except EOFError:
            export_choice = "3"

        if export_choice != "4":
            # Výstupní složka - použití path_manager
            default_output_dir = path_manager.get_charts_path(current_survey_id).replace('/charts/', '/analysis_reports/')
            output_dir = input(f"Výstupní složka (Enter = {default_output_dir}): ").strip()
            if not output_dir:
                output_dir = default_output_dir

            # Zajištění existence adresáře
            os.makedirs(output_dir, exist_ok=True)

            # JSON export
            if export_choice in ["1", "3"]:
                json_path = os.path.join(output_dir, f"{current_survey_id}_analysis_hierarchical.json")
                if manager.export_analysis_report(result, json_path):
                    print(f"✅ JSON report: {json_path}")

            # HTML export
            if export_choice in ["2", "3"]:
                html_generator = HierarchicalAnalysisHTMLGenerator()
                html_path = os.path.join(output_dir, f"{current_survey_id}_analysis_hierarchical.html")
                if html_generator.generate_hierarchical_analysis_report(result, html_path, current_survey_id):
                    print(f"✅ HTML report: {html_path}")

                    # Nabídka otevření v prohlížeči
                    try:
                        open_browser = input("Otevřít HTML report v prohlížeči? (y/N): ").strip().lower()
                        if open_browser == 'y':
                            import webbrowser
                            webbrowser.open(f"file://{os.path.abspath(html_path)}")
                    except EOFError:
                        pass

        print("\n" + "=" * 60)
        print("🎉 Hierarchická analýza dokončena!")
        print("💡 Tip: HTML report zobrazuje přesnou strukturu dotazníku")
        print("💡 Výsledky můžete použít pro automatické generování grafů")
        print("=" * 60)
        
        try:
            input("Stiskněte Enter pro pokračování...")
        except EOFError:
            pass

    except ImportError as e:
        print(f"❌ Analysis Engine není dostupný: {str(e)}")
        print("💡 Zkontrolujte, zda jsou nainstalované všechny závislosti")
    except Exception as e:
        print(f"❌ Neočekávaná chyba: {str(e)}")
        import traceback
        traceback.print_exc()


def report_canvas_menu():
    """Menu 15 - Analytický Report Canvas"""
    try:
        from report_canvas.cli_launcher import launch_report_canvas
        launch_report_canvas()
    except ImportError as e:
        print("❌ Report Canvas modul není dostupný")
        print(f"Chyba: {e}")
        print("💡 Zkontrolujte, zda jsou nainstalované závislosti:")
        print("   pip install PyQt6 openai")
    except Exception as e:
        print(f"❌ Neočekávaná chyba: {str(e)}")
        import traceback
        traceback.print_exc()

def metadata_management_menu():
    """Menu 14 - Správa metadata Analysis Engine"""
    try:
        from analysis_engine import MetadataLoader, MetadataManager

        print("\n" + "=" * 60)
        print("=== 🔧 SPRÁVA METADATA ANALYSIS ENGINE ===")
        print("Metadata Manager v2.0")
        print("=" * 60)

        # Inicializace
        print("🔧 Inicializuji Metadata Manager...")
        loader = MetadataLoader()
        loader.load_all_metadata()

        manager = MetadataManager(loader)

        while True:
            print("\n📋 Dostupné akce:")
            print("1. 📊 Zobrazit souhrn metadata")
            print("2. ✅ Validovat konzistenci metadata")
            print("3. 📁 Generovat registr analýz")
            print("4. 🎨 Generovat registr vizualizací")
            print("5. ➕ Přidat novou analýzu")
            print("6. 🖼️ Přidat novou vizualizaci")
            print("7. 📤 Exportovat kompletní souhrn")
            print("8. 🔍 Detailní informace o typu")
            print("9. 📋 YAML metadata souhrn")
            print("0. Zpět do hlavního menu")

            choice = input("\nVyberte akci: ").strip()

            if choice == "0":
                break
            elif choice == "1":
                # Zobrazit souhrn metadata
                print("\n📊 SOUHRN METADATA:")
                print("=" * 40)
                print(f"Typy otázek: {len(loader.question_types)}")
                print(f"Typy analýz: {len(loader.analysis_types)}")
                print(f"Typy vizualizací: {len(loader.visualization_types)}")
                print(f"Mapování otázek: {len(loader.question_to_analyses)}")
                print(f"Detailní popisy analýz: {len(loader.analysis_descriptions)}")
                print(f"Detailní popisy vizualizací: {len(loader.visualization_descriptions)}")

                # YAML metadata
                yaml_summary = loader.get_yaml_metadata_summary()
                print(f"YAML konfigurace: {yaml_summary['total_configs']}")

            elif choice == "2":
                # Validovat konzistenci
                print("\n✅ VALIDACE KONZISTENCE:")
                print("=" * 40)
                errors = manager.validate_metadata_consistency()

                total_errors = sum(len(v) for v in errors.values())
                if total_errors == 0:
                    print("✅ Všechna metadata jsou konzistentní!")
                else:
                    print(f"❌ Nalezeno {total_errors} problémů:")
                    for category, error_list in errors.items():
                        if error_list:
                            print(f"\n{category.replace('_', ' ').title()}:")
                            for error in error_list[:5]:  # Max 5 chyb na kategorii
                                print(f"  - {error}")
                            if len(error_list) > 5:
                                print(f"  ... a {len(error_list) - 5} dalších")

            elif choice == "3":
                # Generovat registr analýz
                print("\n📁 GENEROVÁNÍ REGISTRU ANALÝZ...")
                if manager.generate_analysis_registry():
                    print("✅ Registr analýz úspěšně vygenerován!")
                else:
                    print("❌ Generování registru selhalo")

            elif choice == "4":
                # Generovat registr vizualizací
                print("\n🎨 GENEROVÁNÍ REGISTRU VIZUALIZACÍ...")
                if manager.generate_visualization_registry():
                    print("✅ Registr vizualizací úspěšně vygenerován!")
                else:
                    print("❌ Generování registru selhalo")

            elif choice == "5":
                # Přidat novou analýzu
                print("\n➕ PŘIDÁNÍ NOVÉ ANALÝZY:")
                print("=" * 40)

                analysis_data = {}
                analysis_data['id'] = input("ID analýzy (např. 'NEW'): ").strip().upper()
                analysis_data['name'] = input("Název analýzy: ").strip()
                analysis_data['description'] = input("Krátký popis: ").strip()
                analysis_data['detailed_description'] = input("Detailní popis (volitelné): ").strip()

                print("Typ analýzy:")
                print("1. question - analýza na úrovni otázek")
                print("2. section - analýza na úrovni sekcí")
                type_choice = input("Vyberte typ (1/2): ").strip()
                analysis_data['analysis_type'] = 'question' if type_choice == '1' else 'section'

                try:
                    priority = int(input("Priorita (1-5, Enter=3): ").strip() or "3")
                    analysis_data['priority'] = max(1, min(5, priority))
                except ValueError:
                    analysis_data['priority'] = 3

                if manager.add_new_analysis_type(analysis_data):
                    print("✅ Nová analýza úspěšně přidána!")
                else:
                    print("❌ Přidání analýzy selhalo")

            elif choice == "6":
                # Přidat novou vizualizaci
                print("\n🖼️ PŘIDÁNÍ NOVÉ VIZUALIZACE:")
                print("=" * 40)

                viz_data = {}
                viz_data['id'] = input("ID vizualizace (např. 'NEW'): ").strip().upper()
                viz_data['name'] = input("Název vizualizace: ").strip()
                viz_data['description'] = input("Krátký popis: ").strip()
                viz_data['detailed_description'] = input("Detailní popis (volitelné): ").strip()
                viz_data['datawrapper_type'] = input("Datawrapper typ (volitelné): ").strip() or None

                try:
                    priority = int(input("Priorita (1-5, Enter=3): ").strip() or "3")
                    viz_data['priority'] = max(1, min(5, priority))
                except ValueError:
                    viz_data['priority'] = 3

                if manager.add_new_visualization_type(viz_data):
                    print("✅ Nová vizualizace úspěšně přidána!")
                else:
                    print("❌ Přidání vizualizace selhalo")

            elif choice == "7":
                # Exportovat kompletní souhrn
                print("\n📤 EXPORT KOMPLETNÍHO SOUHRNU...")
                summary = manager.export_metadata_summary()

                if summary:
                    output_file = f"/home/<USER>/vyvoj/limwrapp/analysis_reports/metadata_summary.json"
                    os.makedirs(os.path.dirname(output_file), exist_ok=True)

                    import json
                    with open(output_file, 'w', encoding='utf-8') as f:
                        json.dump(summary, f, ensure_ascii=False, indent=2)

                    print(f"✅ Souhrn exportován: {output_file}")

                    # Zobrazit základní statistiky
                    stats = summary['metadata_statistics']
                    print(f"\n📊 Základní statistiky:")
                    print(f"   Typy otázek: {stats['total_question_types']}")
                    print(f"   Typy analýz: {stats['total_analysis_types']}")
                    print(f"   Typy vizualizací: {stats['total_visualization_types']}")
                    print(f"   Datawrapper kompatibilita: {summary['visualization_breakdown']['datawrapper_compatibility_rate']:.1%}")
                else:
                    print("❌ Export souhrnu selhal")

            elif choice == "8":
                # Detailní informace o typu
                print("\n🔍 DETAILNÍ INFORMACE:")
                print("1. Analýza")
                print("2. Vizualizace")
                info_choice = input("Vyberte typ (1/2): ").strip()

                if info_choice == "1":
                    analysis_id = input("ID analýzy: ").strip().upper()
                    details = loader.get_analysis_with_details(analysis_id)

                    if details:
                        analysis = details['basic_info']
                        print(f"\n📊 ANALÝZA: {analysis.name}")
                        print(f"ID: {analysis.id}")
                        print(f"Popis: {analysis.description}")
                        print(f"Typ: {analysis.analysis_type}")
                        print(f"Priorita: {analysis.priority}")
                        print(f"Podporované vizualizace: {', '.join(analysis.supported_visualizations)}")

                        if details['detailed_description']:
                            print(f"Detailní popis: {details['detailed_description'][:200]}...")
                    else:
                        print(f"❌ Analýza '{analysis_id}' nenalezena")

                elif info_choice == "2":
                    viz_id = input("ID vizualizace: ").strip().upper()
                    details = loader.get_visualization_with_details(viz_id)

                    if details:
                        viz = details['basic_info']
                        print(f"\n🎨 VIZUALIZACE: {viz.name}")
                        print(f"ID: {viz.id}")
                        print(f"Popis: {viz.description}")
                        print(f"Priorita: {viz.priority}")
                        print(f"Datawrapper typ: {viz.datawrapper_type or 'Externí generátor'}")
                        print(f"Prioritní úroveň: {details['priority_level']}")

                        if details['detailed_description']:
                            print(f"Detailní popis: {details['detailed_description'][:200]}...")

                        # YAML konfigurace
                        yaml_config = details.get('yaml_config')
                        if yaml_config:
                            print("YAML konfigurace: Dostupná")
                    else:
                        print(f"❌ Vizualizace '{viz_id}' nenalezena")

            elif choice == "9":
                # YAML metadata souhrn
                print("\n📋 YAML METADATA SOUHRN:")
                print("=" * 40)
                yaml_summary = loader.get_yaml_metadata_summary()

                print(f"Načtené konfigurace: {yaml_summary['total_configs']}")
                print(f"Konfigurace vizualizací: {yaml_summary['visualization_configs_count']}")

                if yaml_summary['registries']:
                    print("\nRegistr:")
                    for registry in yaml_summary['registries']:
                        print(f"  - {registry['name']}: {registry['items_count']} položek (v{registry['version']})")
                else:
                    print("Žádné registr nenalezeny")

            else:
                print("❌ Neplatná volba")

            if choice != "0":
                input("\nStiskněte Enter pro pokračování...")

    except ImportError as e:
        print(f"❌ Metadata Manager není dostupný: {str(e)}")
        print("💡 Zkontrolujte, zda jsou nainstalované všechny závislosti")
    except Exception as e:
        print(f"❌ Neočekávaná chyba: {str(e)}")
        import traceback
        traceback.print_exc()


def ai_improve_names_menu(tm, chart_data_path):
    """AI úprava názvů pro grafy"""
    print("\n" + "=" * 60)
    print("=== 🤖 AI ÚPRAVA NÁZVŮ PRO GRAFY ===")
    print("=" * 60)
    
    # Kontrola dostupnosti AI
    try:
        from menu.ai_menu_functions import check_ai_availability
        if not check_ai_availability():
            print("❌ AI funkce nejsou dostupné")
            print("💡 Pro aktivaci AI funkcí:")
            print("   1. Nainstalujte závislosti: pip install -r requirements_ai.txt")
            print("   2. Nastavte OPENAI_API_KEY v .env souboru")
            print("   3. Restartujte aplikaci")
            input("\nStiskněte Enter pro návrat...")
            return
    except ImportError:
        print("❌ AI moduly nejsou dostupné")
        print("💡 Spusťte: pip install -r requirements_ai.txt")
        input("\nStiskněte Enter pro návrat...")
        return
    
    try:
        # Import AI komponent a potřebných modulů
        from ai.enhanced_openai_client import EnhancedOpenAIClient
        import json
        import os
        
        # Inicializace AI klienta
        ai_client = EnhancedOpenAIClient()
        
        print("✅ AI klient úspěšně inicializován")
        
        # Extrakce názvů z chart_data.json
        print("\n🔍 Analyzuji existující názvy...")
        strings = tm.extract_translatable_strings(chart_data_path)
        
        question_names = strings.get('question_names', [])
        if not question_names:
            print("❌ Nenalezeny žádné názvy otázek k úpravě")
            input("\nStiskněte Enter pro návrat...")
            return
        
        print(f"📊 Nalezeno {len(question_names)} názvů otázek")
        
        # OPRAVA: Zobrazení prvních 5 názvů pro ukázku (question_names je nyní dict)
        print("\n📋 Ukázka existujících názvů:")
        if isinstance(question_names, dict):
            # question_names je dict {question_code: question_name}
            items = list(question_names.items())[:5]
            for i, (code, name) in enumerate(items, 1):
                print(f"   {i}. [{code}] {name[:80]}{'...' if len(name) > 80 else ''}")
            if len(question_names) > 5:
                print(f"   ... a dalších {len(question_names) - 5} názvů")
        else:
            # Fallback pro starý formát (list)
            for i, name in enumerate(question_names[:5], 1):
                print(f"   {i}. {name[:80]}{'...' if len(name) > 80 else ''}")
            if len(question_names) > 5:
                print(f"   ... a dalších {len(question_names) - 5} názvů")
        
        # Výběr jazyka pro úpravu
        print("\n🌍 Výběr jazyka pro AI úpravu:")
        available_languages = tm.get_available_languages()
        lang_list = list(available_languages.items())
        
        for i, (code, name) in enumerate(lang_list, 1):
            print(f"{i}. {name} ({code})")
        
        try:
            lang_choice = input(f"\nVyberte jazyk [1-{len(lang_list)}]: ").strip()
            if lang_choice.isdigit():
                choice_idx = int(lang_choice) - 1
                if 0 <= choice_idx < len(lang_list):
                    target_language = lang_list[choice_idx][0]
                    target_language_name = lang_list[choice_idx][1]
                else:
                    print("❌ Neplatná volba")
                    return
            else:
                print("❌ Neplatná volba")
                return
        except EOFError:
            return
        
        # OPRAVA: Rozšířený výběr kategorií pro překlad
        print(f"\n🎯 Kategorie pro AI úpravu:")
        print("0. Návrat bez překládání")
        print("1. Pouze hlavní otázky (question_names - bez podotázek)")
        print("2. Pouze podotázky (subquestions)")
        print("3. Pouze škálové odpovědi (scale_responses)")
        print("4. Pouze výběrové odpovědi (choice_responses)")
        print("5. Pouze otevřené textové odpovědi (open_text_responses)")
        print("6. Všechny názvy otázek (hlavní + podotázky)")
        print("7. Všechny kategorie (kompletní překlad)")

        try:
            type_choice = input("Vyberte kategorii [1]: ").strip() or "1"
        except EOFError:
            return
        
        # OPRAVA: Kontrola návrat
        if type_choice == "0":
            print("🔙 Návrat bez překládání")
            return

        # OPRAVA: Příprava dat pro AI podle vybrané kategorie
        names_to_improve = []
        category_name = ""
        target_category = ""

        if type_choice == "1":
            # Pouze hlavní otázky - filtrujemy podotázky
            subquestions = set(strings.get('subquestions', []))
            if isinstance(question_names, dict):
                names_to_improve = [(code, name) for code, name in question_names.items() if name not in subquestions]
            else:
                names_to_improve = [(name, name) for name in question_names if name not in subquestions]
            category_name = "hlavní otázky"
            target_category = "question_names"

        elif type_choice == "2":
            # OPRAVA: Pouze podotázky - používáme kódy jako klíče, texty pro AI
            subquestions = strings.get('subquestions', {})  # Dict {code: text}
            names_to_improve = [(code, text) for code, text in subquestions.items()]
            category_name = "podotázky"
            target_category = "subquestions"

        elif type_choice == "3":
            # Pouze škálové odpovědi
            scale_responses = strings.get('scale_responses', [])
            names_to_improve = [(item, item) for item in scale_responses]
            category_name = "škálové odpovědi"
            target_category = "scale_responses"

        elif type_choice == "4":
            # Pouze výběrové odpovědi
            choice_responses = strings.get('choice_responses', [])
            names_to_improve = [(item, item) for item in choice_responses]
            category_name = "výběrové odpovědi"
            target_category = "choice_responses"

        elif type_choice == "5":
            # Pouze otevřené textové odpovědi
            open_text = strings.get('free_text_responses', {})
            # Flatten všechny textové odpovědi
            all_texts = []
            for question_texts in open_text.values():
                if isinstance(question_texts, dict):
                    for category_texts in question_texts.values():
                        if isinstance(category_texts, list):
                            all_texts.extend(category_texts)
            names_to_improve = [(item, item) for item in set(all_texts)]
            category_name = "otevřené textové odpovědi"
            target_category = "open_text_responses"

        elif type_choice == "6":
            # Všechny názvy otázek (hlavní + podotázky)
            if isinstance(question_names, dict):
                names_to_improve = list(question_names.items())
            else:
                names_to_improve = [(name, name) for name in question_names]
            # Přidáme i podotázky
            subquestions = strings.get('subquestions', [])
            names_to_improve.extend([(item, item) for item in subquestions])
            category_name = "všechny názvy otázek"
            target_category = "mixed"

        elif type_choice == "7":
            # Všechny kategorie
            all_items = []
            # Question names
            if isinstance(question_names, dict):
                all_items.extend(list(question_names.items()))
            else:
                all_items.extend([(name, name) for name in question_names])
            # Ostatní kategorie
            for cat in ['subquestions', 'scale_responses', 'choice_responses']:
                items = strings.get(cat, [])
                all_items.extend([(item, item) for item in items])
            # Open text responses
            open_text = strings.get('free_text_responses', {})
            all_texts = []
            for question_texts in open_text.values():
                if isinstance(question_texts, dict):
                    for category_texts in question_texts.values():
                        if isinstance(category_texts, list):
                            all_texts.extend(category_texts)
            all_items.extend([(item, item) for item in set(all_texts)])
            names_to_improve = all_items
            category_name = "všechny kategorie"
            target_category = "all"
        else:
            print("❌ Neplatná volba")
            return

        print(f"✅ Vybráno {len(names_to_improve)} položek ({category_name})")
        
        if not names_to_improve:
            print("❌ Žádné názvy k úpravě")
            return
        
        # Konfigurace AI promptu
        print(f"\n🤖 Konfigurace AI úpravy pro {target_language_name}:")
        print("AI upraví názvy tak, aby byly:")
        print("   • Vhodné pro zobrazení v grafech")
        print("   • Stručné a výstižné")
        print("   • Gramaticky správné")
        print("   • Konzistentní v terminologii")
        
        # Potvrzení od uživatele
        try:
            confirm = input(f"\nPokračovat s AI úpravou {len(names_to_improve)} názvů? (y/N): ").strip().lower()
            if confirm not in ['y', 'yes', 'ano']:
                print("❌ AI úprava zrušena")
                return
        except EOFError:
            return
        
        # Spuštění AI úpravy
        print(f"\n🚀 Spouštím AI úpravu názvů...")
        print("=" * 50)
        
        improved_names = {}
        processed = 0
        errors = 0
        
        # Zpracování po dávkách (max 10 názvů najednou)
        batch_size = 10
        for i in range(0, len(names_to_improve), batch_size):
            batch = names_to_improve[i:i + batch_size]
            batch_num = (i // batch_size) + 1
            total_batches = (len(names_to_improve) + batch_size - 1) // batch_size
            
            print(f"\n📦 Zpracovávám dávku {batch_num}/{total_batches} ({len(batch)} názvů)...")
            
            try:
                # Příprava promptu pro AI
                prompt = create_ai_improvement_prompt(batch, target_language_name, target_language)
                
                # Volání AI
                messages = [
                    {"role": "system", "content": "Jsi expert na tvorbu názvů pro grafy a vizualizace dat. Tvým úkolem je upravit názvy otázek tak, aby byly vhodné pro zobrazení v grafech - stručné, výstižné a gramaticky správné."},
                    {"role": "user", "content": prompt}
                ]
                
                response = ai_client.chat_completion(
                    messages=messages,
                    temperature=0.3,
                    max_tokens=2000
                )
                
                # Parsování odpovědi
                batch_improvements = parse_ai_response(response.content, batch)
                improved_names.update(batch_improvements)
                
                processed += len(batch)
                print(f"   ✅ Zpracováno {len(batch_improvements)}/{len(batch)} názvů")
                
                # Zobrazení ukázky úprav
                for original, improved in list(batch_improvements.items())[:3]:
                    if original != improved:
                        print(f"      '{original[:40]}...' → '{improved[:40]}...'")
                
            except Exception as e:
                print(f"   ❌ Chyba při zpracování dávky: {e}")
                errors += 1
                # Pokračujeme s dalšími dávkami
                continue
        
        print("\n" + "=" * 50)
        print(f"✅ AI úprava dokončena!")
        print(f"📊 Zpracováno: {processed}/{len(names_to_improve)} názvů")
        print(f"❌ Chyby: {errors} dávek")
        
        # Zobrazení statistik úprav
        actual_changes = sum(1 for orig, improved in improved_names.items() if orig != improved)
        print(f"🔄 Skutečně změněno: {actual_changes} názvů")
        
        if actual_changes == 0:
            print("💡 Žádné názvy nebyly změněny - pravděpodobně jsou již optimální")
            return
        
        # OPRAVA: Uložení úprav bez přepsání existujících překladů
        print(f"\n💾 Ukládání úprav do překladů...")

        # Bezpečné načtení existujícího souboru pro cílový jazyk
        target_file = tm._get_translation_file_path(target_language)
        existing_translations = {}

        if os.path.exists(target_file):
            try:
                with open(target_file, 'r', encoding='utf-8') as f:
                    existing_translations = json.load(f)
                print(f"📂 Načten existující soubor: {target_file}")
            except Exception as e:
                print(f"⚠️  Chyba při načítání existujícího souboru: {e}")
                existing_translations = {}

        # Pokud soubor neexistuje, vytvoříme základní strukturu
        if not existing_translations:
            existing_translations = {
                "metadata": {
                    "survey_id": tm.survey_id,
                    "language": target_language,
                    "created": "auto-generated",
                    "version": "3.0",
                    "structure": "categorized_for_llm_safety"
                },
                "language_settings": {
                    "chart_language": target_language,
                    "available_languages": tm.get_available_languages()
                },
                "question_names": {},
                "subquestions": {},
                "scale_responses": {},
                "choice_responses": {},
                "open_text_responses": {},
                "chart_titles": {}
            }

        # OPRAVA: Debug výpisy před ukládáním
        print(f"\n🔍 DEBUG: Začínám ukládání pro kategorii '{target_category}'")
        print(f"🔍 DEBUG: Počet improved_names: {len(improved_names)}")
        print(f"🔍 DEBUG: Počet names_to_improve: {len(names_to_improve)}")

        # OPRAVA: Aktualizace podle vybrané kategorie, zachování ostatních sekcí
        changes_saved = 0

        # Vytvoříme reverse mapping {name: code} z původních names_to_improve
        name_to_code = {}
        for code, name in names_to_improve:
            name_to_code[name] = code

        if target_category == "question_names":
            # Pouze question_names
            if 'question_names' not in existing_translations:
                existing_translations['question_names'] = {}

            for original, improved in improved_names.items():
                if original != improved:
                    question_code = name_to_code.get(original, original)
                    existing_translations['question_names'][question_code] = improved
                    changes_saved += 1

        elif target_category == "subquestions":
            # OPRAVA: Speciální zpracování pro subquestions (kód jako klíč)
            print(f"🔍 DEBUG: Ukládám kategorii 'subquestions'")
            print(f"🔍 DEBUG: Počet improved_names: {len(improved_names)}")

            if 'subquestions' not in existing_translations:
                existing_translations['subquestions'] = {}
                print(f"🔍 DEBUG: Vytvořena nová sekce 'subquestions'")
            else:
                print(f"🔍 DEBUG: Sekce 'subquestions' už existuje s {len(existing_translations['subquestions'])} položkami")

            # OPRAVA: Pro subquestions máme mapování (kód, text) → překlad
            for original_text, improved_text in improved_names.items():
                if original_text != improved_text:
                    # Najdeme kód pro tento text
                    subq_code = name_to_code.get(original_text)
                    if subq_code:
                        existing_translations['subquestions'][subq_code] = improved_text
                        changes_saved += 1
                        print(f"🔍 DEBUG: Uloženo [{subq_code}]: '{original_text[:30]}...' → '{improved_text[:30]}...'")
                    else:
                        print(f"🔍 DEBUG: CHYBA - nenalezen kód pro: '{original_text[:30]}...'")
                else:
                    print(f"🔍 DEBUG: Beze změny: '{original_text[:50]}...'")

            print(f"🔍 DEBUG: Celkem uloženo {changes_saved} změn do 'subquestions'")

        elif target_category in ["scale_responses", "choice_responses"]:
            # Ostatní jednoduché kategorie
            if target_category not in existing_translations:
                existing_translations[target_category] = {}

            for original, improved in improved_names.items():
                if original != improved:
                    existing_translations[target_category][original] = improved
                    changes_saved += 1

        elif target_category == "open_text_responses":
            # Speciální zpracování pro open text responses
            if 'open_text_responses' not in existing_translations:
                existing_translations['open_text_responses'] = {}

            for original, improved in improved_names.items():
                if original != improved:
                    existing_translations['open_text_responses'][original] = improved
                    changes_saved += 1

        elif target_category == "mixed":
            # Kombinace question_names + subquestions
            if 'question_names' not in existing_translations:
                existing_translations['question_names'] = {}
            if 'subquestions' not in existing_translations:
                existing_translations['subquestions'] = {}

            for original, improved in improved_names.items():
                if original != improved:
                    question_code = name_to_code.get(original, original)
                    # Pokud je to question_code, jde do question_names
                    if question_code != original and question_code in [code for code, _ in names_to_improve]:
                        existing_translations['question_names'][question_code] = improved
                    else:
                        # Jinak jde do subquestions
                        existing_translations['subquestions'][original] = improved
                    changes_saved += 1

        elif target_category == "all":
            # Všechny kategorie - inteligentní rozdělení
            for cat in ['question_names', 'subquestions', 'scale_responses', 'choice_responses', 'open_text_responses']:
                if cat not in existing_translations:
                    existing_translations[cat] = {}

            for original, improved in improved_names.items():
                if original != improved:
                    question_code = name_to_code.get(original, original)

                    # Rozhodnutí do které kategorie patří
                    if question_code != original and question_code in [code for code, _ in names_to_improve]:
                        existing_translations['question_names'][question_code] = improved
                    elif original in strings.get('subquestions', []):
                        existing_translations['subquestions'][original] = improved
                    elif original in strings.get('scale_responses', []):
                        existing_translations['scale_responses'][original] = improved
                    elif original in strings.get('choice_responses', []):
                        existing_translations['choice_responses'][original] = improved
                    else:
                        existing_translations['open_text_responses'][original] = improved
                    changes_saved += 1

        # Uložení s zachováním všech existujících sekcí
        try:
            os.makedirs(os.path.dirname(target_file), exist_ok=True)
            with open(target_file, 'w', encoding='utf-8') as f:
                json.dump(existing_translations, f, ensure_ascii=False, indent=2)
            print(f"✅ Uloženo {changes_saved} AI úprav do {target_file}")
            print(f"🔒 Zachovány všechny existující sekce překladů")
        except Exception as e:
            print(f"❌ Chyba při ukládání: {e}")
            return

        # OPRAVA: Nabídka aplikace překladů s jasným vysvětlením
        try:
            print(f"\n💡 AI úpravy byly uloženy do: {target_file}")
            print(f"📊 Původní chart_data.json zůstává nezměněn")

            output_path = chart_data_path.replace('.json', f'_ai_improved_{target_language}.json')
            print(f"🎯 Nová jazyková verze bude: {output_path.split('/')[-1]}")

            apply_now = input(f"\nVytvořit jazykovou verzi chart_data nyní? (y/N): ").strip().lower()
            if apply_now in ['y', 'yes', 'ano']:
                # OPRAVA: Debug výpisy pro diagnostiku
                print(f"🔍 DEBUG: Aplikuji překlady...")
                print(f"🔍 DEBUG: Source: {chart_data_path.split('/')[-1]}")
                print(f"🔍 DEBUG: Target: {output_path.split('/')[-1]}")
                print(f"🔍 DEBUG: Translation file: {target_file.split('/')[-1]}")
                print(f"🔍 DEBUG: Počet kategorií v existing_translations: {len(existing_translations)}")

                # Pro aplikaci překladů musíme dočasně načíst upravený soubor
                original_file = tm.translation_file
                original_translations = tm.translations

                tm.translation_file = target_file
                tm.translations = existing_translations

                print(f"🔍 DEBUG: Spouštím apply_translations...")
                result = tm.apply_translations(chart_data_path, output_path)
                print(f"🔍 DEBUG: Výsledek apply_translations: {result}")

                if result:
                    print(f"✅ Jazyková verze vytvořena: {output_path.split('/')[-1]}")
                    print(f"📁 Původní chart_data.json zůstává nezměněn")
                    print(f"🌍 Pro další jazyky můžeš použít původní chart_data.json")

                    # Kontrola, zda soubor skutečně existuje
                    import os
                    if os.path.exists(output_path):
                        file_size = os.path.getsize(output_path)
                        print(f"📊 Soubor vytvořen: {file_size} bytů")
                    else:
                        print(f"❌ PROBLÉM: Soubor nebyl vytvořen!")
                else:
                    print("❌ Chyba při vytváření jazykové verze")

                # Obnovíme původní nastavení
                tm.translation_file = original_file
                tm.translations = original_translations
        except EOFError:
            pass
        
        # Zobrazení nákladů
        try:
            stats = ai_client.get_usage_stats()
            print(f"\n💰 Náklady AI úpravy: ~${stats['total_cost']:.4f}")
        except:
            print("\n💰 Náklady AI úpravy: ~$0.0001-0.001 (gpt-4o-mini)")
        
    except ImportError as e:
        print(f"❌ Chybí AI moduly: {e}")
        print("💡 Spusťte: pip install -r requirements_ai.txt")
    except Exception as e:
        print(f"❌ Neočekávaná chyba: {e}")
        import traceback
        traceback.print_exc()
    
    input("\nStiskněte Enter pro návrat...")


def create_ai_improvement_prompt(names_batch, target_language_name, target_language_code):
    """Vytvoří prompt pro AI úpravu názvů"""
    # OPRAVA: names_batch je nyní list of tuples (code, name)
    # Nepoužíváme hranaté závorky v promptu, aby AI je nekopíroval
    names_list = "\n".join([f"{i+1}. {name}" for i, (code, name) in enumerate(names_batch)])
    
    prompt = f"""Úkol: Uprav následující názvy otázek z průzkumu tak, aby byly vhodné pro zobrazení v grafech.

Cílový jazyk: {target_language_name} ({target_language_code})

Požadavky na úpravu:
1. Názvy musí být STRUČNÉ (max 60 znaků)
2. Musí být VÝSTIŽNÉ a jasně sdělit, o čem otázka je
3. Musí být gramaticky správné v cílovém jazyce
4. Musí být konzistentní v terminologii
5. Vhodné pro zobrazení jako nadpis grafu
6. Pokud je název již optimální, ponech ho beze změny

Původní názvy otázek:
{names_list}

Odpověz ve formátu JSON:
{{
  "improvements": {{
    "původní název 1": "upravený název 1",
    "původní název 2": "upravený název 2",
    ...
  }}
}}

Pokud název nepotřebuje úpravu, použij stejný text jako původní."""
    
    return prompt


def parse_ai_response(ai_response, original_names):
    """Parsuje odpověď AI a vrátí slovník úprav"""
    try:
        import json
        import re
        
        # Pokusíme se najít JSON v odpovědi
        json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
        if json_match:
            json_str = json_match.group()
            data = json.loads(json_str)
            
            if 'improvements' in data:
                return data['improvements']
            else:
                return data
        else:
            # Fallback - pokud AI nevrátilo JSON, vrátíme původní názvy
            print("⚠️ AI nevrátilo validní JSON, ponechávám původní názvy")
            # OPRAVA: original_names je nyní list of tuples (code, name)
            return {name: name for _, name in original_names}

    except Exception as e:
        print(f"⚠️ Chyba při parsování AI odpovědi: {e}")
        # OPRAVA: Fallback - vrátíme původní názvy
        return {name: name for _, name in original_names}


def privacy_filter_menu():
    """Menu pro konfiguraci privacy filtru"""
    global current_survey_id
    
    # Kontrola nastavení
    if not check_server_and_survey_required():
        return
    
    try:
        from privacy_filter import PrivacyFilter
        
        print("\n" + "=" * 60)
        print("=== 🔒 OCHRANA SOUKROMÍ - FILTROVÁNÍ SLOUPCŮ ===")
        print(f"Průzkum: {current_survey_id}")
        print("=" * 60)
        
        # Inicializace privacy filtru
        privacy_filter = PrivacyFilter(current_survey_id)
        
        # Kontrola existence CSV souboru
        csv_path = path_manager.get_data_path(current_survey_id, "responses.csv")
        if not os.path.exists(csv_path):
            print(f"❌ CSV soubor neexistuje: {csv_path}")
            print("💡 Nejprve načtěte data průzkumu (Menu 2)")
            return
        
        while True:
            # Načtení informací o sloupcích
            columns_info = privacy_filter.get_csv_columns_info(csv_path)
            filter_summary = privacy_filter.get_filter_summary(csv_path)
            
            print(f"\n📊 Souhrn CSV souboru:")
            print(f"   Celkem sloupců: {filter_summary.get('total_columns', len(columns_info))}")
            print(f"   Vyloučených sloupců: {filter_summary['excluded_count']}")
            print(f"   Zpracovávaných sloupců: {filter_summary.get('processed_columns', len(columns_info) - filter_summary['excluded_count'])}")
            
            if filter_summary['excluded_columns']:
                excluded_str = ', '.join(map(str, filter_summary['excluded_columns']))
                print(f"   Vyloučené pozice: {excluded_str}")
            
            print("\n📋 Dostupné akce:")
            print("1. 👁️  Zobrazit všechny sloupce")
            print("2. ⚙️  Nastavit vyloučené sloupce")
            print("3. 🧹 Vymazat všechna nastavení")
            print("4. 📄 Zobrazit souhrn nastavení")
            print("5. 🧪 Test filtrování (náhled)")
            print("6. 📝 Vyloučit všechny textové otázky")
            print("0. Zpět do hlavního menu")
            
            try:
                choice = input("\nVyberte akci: ").strip()
            except EOFError:
                break
            
            if choice == "0":
                break
            elif choice == "1":
                # Zobrazit všechny sloupce
                print(f"\n📋 Všechny sloupce v CSV souboru ({len(columns_info)} celkem):")
                print("=" * 80)
                
                for col_info in columns_info:
                    status = "❌ VYLOUČEN" if col_info['is_excluded'] else "✅ zpracováván"
                    # OPRAVA: Použijeme human_name místo technického name
                    display_name = col_info['human_name'] if col_info['human_name'] != col_info['name'] else col_info['name']
                    print(f"{col_info['index']:3d}. {display_name[:60]:<60} {status}")

                    # Pokud je lidský název jiný než technický, zobrazíme i technický v závorce
                    if col_info['human_name'] != col_info['name'] and len(col_info['human_name']) > 0:
                        print(f"     ({col_info['name']})")
                
                # Stránkování pro velké množství sloupců
                if len(columns_info) > 20:
                    print(f"\n💡 Zobrazeno všech {len(columns_info)} sloupců")
                    print("💡 Pro lepší přehled použijte akci 2 pro konfiguraci")
            
            elif choice == "2":
                # Nastavit vyloučené sloupce
                print(f"\n⚙️ KONFIGURACE VYLOUČENÝCH SLOUPCŮ")
                print("=" * 50)
                print("💡 Zadejte čísla sloupců, které chcete VYLOUČIT ze zpracování")
                print("💡 Formát: jednotlivá čísla nebo rozsahy oddělené čárkami")
                print("💡 Příklad: 1-4,8,64-78")
                print("💡 Prázdný vstup = žádné vyloučení")
                
                # Zobrazení aktuálního nastavení
                if filter_summary['excluded_columns']:
                    current_excluded = ', '.join(map(str, filter_summary['excluded_columns']))
                    print(f"\n📋 Aktuálně vyloučeno: {current_excluded}")
                else:
                    print(f"\n📋 Aktuálně: žádné sloupce nejsou vyloučeny")
                
                # Zobrazení prvních a posledních sloupců pro orientaci
                print(f"\n📊 Orientační přehled sloupců:")
                print("Začátek souboru:")
                for col_info in columns_info[:5]:
                    # OPRAVA: Použijeme human_name pro lepší orientaci
                    display_name = col_info['human_name'] if col_info['human_name'] != col_info['name'] else col_info['name']
                    print(f"   {col_info['index']:3d}. {display_name[:50]}")

                if len(columns_info) > 10:
                    print("   ...")
                    print("Konec souboru:")
                    for col_info in columns_info[-5:]:
                        # OPRAVA: Použijeme human_name pro lepší orientaci
                        display_name = col_info['human_name'] if col_info['human_name'] != col_info['name'] else col_info['name']
                        print(f"   {col_info['index']:3d}. {display_name[:50]}")
                
                try:
                    range_input = input(f"\nZadejte rozsah vyloučených sloupců: ").strip()
                    
                    if privacy_filter.set_excluded_columns(range_input):
                        print("✅ Nastavení úspěšně uloženo")
                        
                        # Zobrazení nového souhrnu
                        new_summary = privacy_filter.get_filter_summary(csv_path)
                        print(f"📊 Nové nastavení:")
                        print(f"   Vyloučeno: {new_summary['excluded_count']} sloupců")
                        print(f"   Zpracováváno: {new_summary.get('processed_columns', 0)} sloupců")
                    else:
                        print("❌ Chyba při ukládání nastavení")
                        
                except EOFError:
                    pass
            
            elif choice == "3":
                # Vymazat všechna nastavení
                print(f"\n🧹 VYMAZÁNÍ NASTAVENÍ")
                print("⚠️  Tato akce vymaže všechna nastavení privacy filtru")
                
                try:
                    confirm = input("Opravdu chcete vymazat všechna nastavení? (ano/ne): ").strip().lower()
                    if confirm in ['ano', 'yes', 'y']:
                        if privacy_filter.clear_filter():
                            print("✅ Všechna nastavení byla vymazána")
                        else:
                            print("❌ Chyba při mazání nastavení")
                    else:
                        print("❌ Mazání zrušeno")
                except EOFError:
                    pass
            
            elif choice == "4":
                # Zobrazit souhrn nastavení
                print(f"\n📄 DETAILNÍ SOUHRN NASTAVENÍ")
                print("=" * 50)
                
                summary = privacy_filter.get_filter_summary(csv_path)
                
                print(f"Survey ID: {summary['survey_id']}")
                print(f"Filtr aktivní: {'Ano' if summary['filter_active'] else 'Ne'}")
                print(f"Celkem sloupců: {summary.get('total_columns', 'N/A')}")
                print(f"Vyloučených sloupců: {summary['excluded_count']}")
                print(f"Zpracovávaných sloupců: {summary.get('processed_columns', 'N/A')}")
                
                if summary.get('excluded_column_names'):
                    print(f"\nVyloučené sloupce:")
                    for i, name in enumerate(summary['excluded_column_names'], 1):
                        print(f"   {i}. {name}")
                
                # Informace o souboru nastavení
                filter_file = privacy_filter.filter_file
                if os.path.exists(filter_file):
                    file_size = os.path.getsize(filter_file)
                    print(f"\nSoubor nastavení: {filter_file}")
                    print(f"Velikost: {file_size} bytů")
                else:
                    print(f"\nSoubor nastavení: neexistuje (použijí se výchozí hodnoty)")
            
            elif choice == "5":
                # Test filtrování
                print(f"\n🧪 TEST FILTROVÁNÍ - NÁHLED")
                print("=" * 50)
                
                if not filter_summary['filter_active']:
                    print("ℹ️  Žádné sloupce nejsou vyloučeny - filtr není aktivní")
                    continue
                
                try:
                    import pandas as pd
                    
                    # Načteme jen prvních 5 řádků pro náhled
                    df_sample = pd.read_csv(csv_path, sep=';', nrows=5)
                    print(f"📊 Původní data (prvních 5 řádků, {len(df_sample.columns)} sloupců):")
                    print(f"   Sloupce: {', '.join(df_sample.columns[:5].tolist())}{'...' if len(df_sample.columns) > 5 else ''}")
                    
                    # Aplikace filtru
                    df_filtered = privacy_filter.filter_dataframe(df_sample)
                    print(f"\n📊 Filtrovaná data ({len(df_filtered.columns)} sloupců):")
                    print(f"   Sloupce: {', '.join(df_filtered.columns[:5].tolist())}{'...' if len(df_filtered.columns) > 5 else ''}")
                    
                    removed_count = len(df_sample.columns) - len(df_filtered.columns)
                    print(f"\n✅ Odstraněno {removed_count} sloupců z {len(df_sample.columns)}")
                    
                except ImportError:
                    print("❌ Pandas knihovna není dostupná pro test")
                except Exception as e:
                    print(f"❌ Chyba při testu: {e}")

            elif choice == "6":
                # Vyloučit všechny textové otázky
                print(f"\n📝 VYLOUČENÍ TEXTOVÝCH OTÁZEK")
                print("=" * 50)
                print("💡 Tato akce automaticky vyloučí všechny hlavní textové otázky ze zpracování")
                print("💡 Textové otázky jsou otázky typu 'T' (dlouhý text) a 'S' (krátký text)")
                print("💡 Podotázky array otázek zůstanou zachovány")

                try:
                    # Najdeme textové otázky
                    textual_columns = privacy_filter.find_textual_questions(csv_path)

                    if not textual_columns:
                        print("ℹ️  Žádné textové otázky nebyly nalezeny")
                        continue

                    print(f"\n📊 Nalezeno {len(textual_columns)} textových otázek:")
                    for i, col_info in enumerate(textual_columns[:10], 1):  # Zobrazíme prvních 10
                        display_name = col_info['human_name'] if col_info['human_name'] != col_info['name'] else col_info['name']
                        print(f"   {i:2d}. [{col_info['index']:3d}] {display_name[:60]}")

                    if len(textual_columns) > 10:
                        print(f"   ... a dalších {len(textual_columns) - 10} textových otázek")

                    # Potvrzení
                    confirm = input(f"\nChcete vyloučit všech {len(textual_columns)} textových otázek? (ano/ne): ").strip().lower()
                    if confirm in ['ano', 'yes', 'y']:
                        # Získáme indexy textových sloupců
                        textual_indices = [col['index'] for col in textual_columns]

                        # Převedeme na range string
                        range_string = ','.join(map(str, textual_indices))

                        if privacy_filter.set_excluded_columns(range_string):
                            print(f"✅ Úspěšně vyloučeno {len(textual_columns)} textových otázek")

                            # Zobrazení nového souhrnu
                            new_summary = privacy_filter.get_filter_summary(csv_path)
                            print(f"📊 Nové nastavení:")
                            print(f"   Vyloučeno: {new_summary['excluded_count']} sloupců")
                            print(f"   Zpracováváno: {new_summary.get('processed_columns', 0)} sloupců")
                        else:
                            print("❌ Chyba při ukládání nastavení")
                    else:
                        print("❌ Vyloučení zrušeno")

                except Exception as e:
                    print(f"❌ Chyba při hledání textových otázek: {e}")
                    print("💡 Zkontrolujte, zda existuje question_mapping.csv")

            else:
                print("❌ Neplatná volba")
            
            if choice != "0":
                input("\nStiskněte Enter pro pokračování...")
    
    except ImportError:
        print("❌ Privacy filter modul není dostupný")
        print("💡 Zkontrolujte, zda je soubor src/privacy_filter.py správně vytvořen")
    except Exception as e:
        print(f"❌ Neočekávaná chyba: {str(e)}")
        import traceback
        traceback.print_exc()


def survey_structure_diagnostics():
    """Menu 17 - Diagnostika struktury průzkumu"""
    if not current_survey_id:
        print("❌ Nejprve nastavte server a průzkum (Menu 11)")
        return

    from survey_diagnostics import (
        analyze_lss_structure, analyze_csv_columns, check_lss_csv_mapping,
        check_translations, detect_repeating_questions,
        generate_complete_diagnostic_report, generate_corrected_mapping
    )

    print("\n" + "=" * 60)
    print("=== DIAGNOSTIKA STRUKTURY PRŮZKUMU ===")
    print(f"Průzkum: {current_survey_id}")
    print("=" * 60)

    # Cesty k souborům
    lss_path = path_manager.get_data_path(current_survey_id, 'structure.lss')
    csv_path = path_manager.get_data_path(current_survey_id, 'responses.csv')
    mapping_path = path_manager.get_data_path(current_survey_id, 'question_mapping.csv')
    translations_path = path_manager.get_data_path(current_survey_id, 'translations_cs-CZ.json')

    print(f"\n📁 Kontrolované soubory:")
    print(f"   LSS struktura: {lss_path}")
    print(f"   CSV data: {csv_path}")
    print(f"   Mapování: {mapping_path}")
    print(f"   Překlady: {translations_path}")

    try:
        while True:
            print("\n📋 Dostupné diagnostiky:")
            print("1. 🏗️  Analýza LSS struktury (skupiny a otázky)")
            print("2. 📊 Analýza CSV sloupců")
            print("3. 🔗 Kontrola mapování LSS ↔ CSV")
            print("4. 🌍 Kontrola překladů")
            print("5. 🔍 Detekce opakujících se otázek")
            print("6. 📋 Kompletní diagnostický report")
            print("7. 🛠️  Generování opravného mapování")
            print("0. Zpět do hlavního menu")

            try:
                choice = input("\nVyberte diagnostiku: ").strip()
            except EOFError:
                break

            if choice == "0":
                break
            elif choice == "1":
                analyze_lss_structure(lss_path)
            elif choice == "2":
                analyze_csv_columns(csv_path)
            elif choice == "3":
                check_lss_csv_mapping(lss_path, csv_path, mapping_path)
            elif choice == "4":
                check_translations(translations_path, mapping_path)
            elif choice == "5":
                detect_repeating_questions(lss_path)
            elif choice == "6":
                generate_complete_diagnostic_report(lss_path, csv_path, mapping_path, translations_path)
            elif choice == "7":
                generate_corrected_mapping(lss_path, csv_path)
            else:
                print("❌ Neplatná volba")

            if choice != "0":
                input("\nStiskněte Enter pro pokračování...")

    except KeyboardInterrupt:
        print("\n❌ Přerušeno uživatelem")
    except Exception as e:
        print(f"❌ Neočekávaná chyba: {e}")
        import traceback
        traceback.print_exc()


def main():
    # Inicializace aplikace při spuštění
    print("Inicializuji aplikaci...")
    if not app_initializer.initialize():
        print("✗ Chyba při inicializaci aplikace")
        return

    # Zobrazení reportu inicializace
    report = app_initializer.get_initialization_report()
    print(f"✓ Aplikace inicializována - {report['total_surveys']} průzkumů na {report['servers_configured']+1} serverech")

    while True:
        display_menu()
        try:
            choice = input("Vyberte volbu: ")
        except EOFError:
            print("Ukončuji program (EOF)")
            break
        if choice == "0":
            print("Ukončuji program")
            break
        elif choice == "1":
            list_surveys()
        elif choice == "2":
            load_survey_data()
        elif choice == "3":
            load_survey_structure()
        elif choice == "4":
            merge_csv_json()
        elif choice == "5":
            transform_to_long()
        elif choice == "6":
            generate_chart_structure()
        elif choice == "7":
            generate_charts_menu()
        elif choice == "8":
            generate_enhanced_charts_menu()
        elif choice == "9":
            download_png_charts()
        elif choice == "10":
            translation_management_menu()
        elif choice == "11":
            select_server_and_survey()
        elif choice == "12":
            datawrapper_export_menu()
        elif choice == "13":
            intelligent_analysis_menu()
        elif choice == "14":
            metadata_management_menu()
        elif choice == "15":
            report_canvas_menu()
        elif choice == "16":
            privacy_filter_menu()
        elif choice == "17":
            survey_structure_diagnostics()
        # AI funkce - BEZPEČNÉ rozšíření
        elif choice == "20":
            try:
                from menu.ai_menu_functions import menu_ai_wordcloud
                menu_ai_wordcloud()
            except ImportError:
                print("❌ AI moduly nejsou dostupné")
                print("💡 Spusťte: pip install -r requirements_ai.txt")
            except Exception as e:
                print(f"❌ Chyba AI funkce: {e}")
        elif choice == "21":
            try:
                from menu.ai_menu_functions import menu_chart_type_configuration
                menu_chart_type_configuration()
            except (ImportError, AttributeError):
                print("⚠️ Funkce konfigurace typů grafů není ještě implementována")
            except Exception as e:
                print(f"❌ Chyba: {e}")
        elif choice == "22":
            try:
                from menu.ai_menu_functions import menu_virtual_questions
                menu_virtual_questions()
            except (ImportError, AttributeError):
                print("⚠️ Funkce správy virtuálních otázek není ještě implementována")
            except Exception as e:
                print(f"❌ Chyba: {e}")
        elif choice == "23":
            try:
                from menu.ai_menu_functions import menu_ai_graph_analysis
                menu_ai_graph_analysis()
            except (ImportError, AttributeError):
                print("⚠️ Funkce AI analýzy grafů není ještě implementována")
            except Exception as e:
                print(f"❌ Chyba: {e}")
        elif choice == "24":
            try:
                from menu.ai_menu_functions import menu_chart_settings_reset
                menu_chart_settings_reset()
            except (ImportError, AttributeError):
                print("⚠️ Funkce reset nastavení grafů není ještě implementována")
            except Exception as e:
                print(f"❌ Chyba: {e}")
        elif choice == "99":
            display_log()
        else:
            print("Neplatná volba")

if __name__ == "__main__":
    main()
