import requests
import json
import os
from datetime import datetime
from typing import Optional
try:
    from dotenv import load_dotenv
except ImportError:
    def load_dotenv():
        pass
from config_loader import load_config
from logger import get_logger

load_dotenv()
logger = get_logger(__name__)

class LimeSurveyClient:
    def __init__(self, data_dir: str = "data"):
        """
        Inicializace klienta s konfigurací z .env
        
        Args:
            data_dir: Adres<PERSON>ř pro ukládání stažených dat
        """
        config = load_config()
        self.api_url = config['LIMESURVEY']['API_URL']
        self.username = config['LIMESURVEY']['USERNAME']
        self.password = config['LIMESURVEY']['PASSWORD']
        self.session_key = None
        self.data_dir = data_dir
        
        if not all([self.api_url, self.username, self.password]):
            logger.warning("Chybí konfigurace LimeSurvey v .env, přechod do test módu")
            
        # Vyt<PERSON><PERSON><PERSON><PERSON> hlavního adresáře pro data
        os.makedirs(self.data_dir, exist_ok=True)
        logger.info(f"Inicializován LimeSurvey klient (data_dir: {self.data_dir})")
        
    def _make_request(self, method, params):
        """Základní metoda pro volání API s lepším zpracováním chyb"""
        payload = {
            "method": method,
            "params": params,
            "id": 1
        }
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        try:
            response = requests.post(
                self.api_url,
                data=json.dumps(payload),
                headers=headers,
                timeout=30
            )
            
            # Kontrola status kódu
            if response.status_code != 200:
                logger.error(f"API Error: {response.status_code}")
                logger.error(f"Request method: {method}")
                logger.error(f"Request params: {params}")
                logger.error(f"Response: {response.text[:1000]}")
                return None
                
            # Zpracování JSON odpovědi
            try:
                json_data = response.json()
                if json_data.get('error'):
                    logger.error(f"API Error: {json_data['error']}")
                    return None
                
                result = json_data.get('result')
                if isinstance(result, str):
                    try:
                        return json.loads(result)
                    except json.JSONDecodeError:
                        return result
                return result
            except json.JSONDecodeError as e:
                logger.error(f"JSON Decode Error: {str(e)}")
                logger.debug(f"Response Content: {response.text[:500]}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Request Error: {str(e)}")
            return None

    def get_session_key(self):
        """Získání session key pro RPC API pomocí uživatelského jména a hesla"""
        if not self.session_key:
            self.session_key = self._make_request("get_session_key", [self.username, self.password])
        return self.session_key

    def get_responses(self, survey_id: str, completed_only: bool = False,
        start_date: Optional[datetime] = None, end_date: Optional[datetime] = None) -> str:
        """
        Stažení dat průzkumu a uložení do CSV
        
        Args:
            survey_id: ID průzkumu
            completed_only: Pouze dokončené odpovědi
            start_date: Počáteční datum
            end_date: Koncové datum
            
        Returns:
            Cesta k uloženému CSV souboru
        """
        logger.info(f"Stahuji odpovědi pro průzkum {survey_id}")
        
        # Příprava parametrů
        params = [
            self.get_session_key(),
            survey_id,
            'csv',
            None,  # headingType
            'complete' if completed_only else 'all',  # responseType
            'en',  # lang
            True,  # completionStatus
            start_date.strftime("%Y-%m-%d %H:%M:%S") if start_date else None,
            end_date.strftime("%Y-%m-%d %H:%M:%S") if end_date else None
        ]
        
        # Stažení dat
        data = self._make_request("export_responses", params)
        if not data:
            raise ValueError(f"Nepodařilo se stáhnout data pro průzkum {survey_id}")
            
        # Převod dat na string pokud je to dict
        if isinstance(data, dict):
            data = json.dumps(data, ensure_ascii=False)
            
        # Uložení do souboru
        csv_path = os.path.join(self.data_dir, f"survey_{survey_id}_responses.csv")
        with open(csv_path, 'w', encoding='utf-8') as f:
            f.write(str(data))
            
        # Dekomprese a validace CSV
        from data_transformer import decompress_csv, validate_csv_structure
        decompress_csv(csv_path)
        validate_csv_structure(csv_path)
            
        logger.info(f"Data uložena a validována do {csv_path}")
        return csv_path
import requests
import json
import os
from datetime import datetime
from typing import Optional
try:
    try:
        from dotenv import load_dotenv
    except ImportError:
        def load_dotenv():
            pass
except ImportError:
    def load_dotenv():
        pass
from config_loader import load_config
from logger import get_logger

load_dotenv()
logger = get_logger(__name__)

class LimeSurveyClient:
    def __init__(self, data_dir: str = "data"):
        """
        Inicializace klienta s konfigurací z .env
        
        Args:
            data_dir: Adresář pro ukládání stažených dat
        """
        config = load_config()
        self.api_url = config['LIMESURVEY']['API_URL']
        self.username = config['LIMESURVEY']['USERNAME']
        self.password = config['LIMESURVEY']['PASSWORD']
        self.session_key = None

        # Použití PROJECT_ROOT z .env pro data_dir
        project_root = os.environ.get('PROJECT_ROOT', os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        self.data_dir = os.path.join(project_root, data_dir) if not os.path.isabs(data_dir) else data_dir

        if not all([self.api_url, self.username, self.password]):
            raise ValueError("Chybí konfigurace LimeSurvey v .env")

        # Vytvoření adresáře pro data
        os.makedirs(self.data_dir, exist_ok=True)
        logger.info(f"Inicializován LimeSurvey klient (data_dir: {self.data_dir})")
        
    def _make_request(self, method, params):
        """Základní metoda pro volání API s lepším zpracováním chyb"""
        payload = {
            "method": method,
            "params": params,
            "id": 1
        }
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        try:
            response = requests.post(
                self.api_url,
                data=json.dumps(payload),
                headers=headers,
                timeout=30
            )
            
            # Kontrola status kódu
            if response.status_code != 200:
                logger.error(f"API Error: {response.status_code}")
                logger.debug(f"Response: {response.text[:500]}")
                return None
                
            # Zpracování JSON odpovědi
            try:
                json_data = response.json()
                if json_data.get('error'):
                    logger.error(f"API Error: {json_data['error']}")
                    return None
                
                result = json_data.get('result')
                if isinstance(result, str):
                    try:
                        return json.loads(result)
                    except json.JSONDecodeError:
                        return result
                return result
            except json.JSONDecodeError as e:
                logger.error(f"JSON Decode Error: {str(e)}")
                logger.debug(f"Response Content: {response.text[:500]}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Request Error: {str(e)}")
            return None

    def get_session_key(self):
        """Získání session key pro RPC API pomocí uživatelského jména a hesla"""
        if not self.session_key:
            self.session_key = self._make_request("get_session_key", [self.username, self.password])
        return self.session_key

    def get_responses(self, survey_id: str, completed_only: bool = False,
        start_date: Optional[datetime] = None, end_date: Optional[datetime] = None) -> str:
        """
        Stažení dat průzkumu a uložení do CSV
        
        Args:
            survey_id: ID průzkumu
            completed_only: Pouze dokončené odpovědi
            start_date: Počáteční datum
            end_date: Koncové datum
            
        Returns:
            Cesta k uloženému CSV souboru
        """
        logger.info(f"Stahuji odpovědi pro průzkum {survey_id}")
        
        # Vytvoření podadresáře pro průzkum
        survey_dir = os.path.join(self.data_dir, survey_id)
        os.makedirs(survey_dir, exist_ok=True)
        
        # Příprava parametrů
        params = [
            self.get_session_key(),
            survey_id,
            'csv',
            None,  # headingType
            'complete' if completed_only else 'all',  # responseType
            'en',  # lang
            True,  # completionStatus
            start_date.strftime("%Y-%m-%d %H:%M:%S") if start_date else None,
            end_date.strftime("%Y-%m-%d %H:%M:%S") if end_date else None
        ]
        
        # Stažení dat
        data = self._make_request("export_responses", params)
        if not data:
            raise ValueError(f"Nepodařilo se stáhnout data pro průzkum {survey_id}")
            
        # Převod dat na string pokud je to dict
        if isinstance(data, dict):
            data = json.dumps(data, ensure_ascii=False)
            
        # Uložení do souboru
        csv_path = os.path.join(survey_dir, f"responses.csv")
        with open(csv_path, 'w', encoding='utf-8') as f:
            f.write(str(data))
            
        # Dekomprese a validace CSV
        from data_transformer import decompress_csv, validate_csv_structure
        decompress_csv(csv_path)
        validate_csv_structure(csv_path)
            
        logger.info(f"Data uložena do {csv_path}")
        return csv_path

    def get_survey_structure(self, survey_id: str) -> str:
        """Získání struktury průzkumu - preferuje XML LSS, fallback na JSON"""
        survey_dir = os.path.join(self.data_dir, survey_id)
        os.makedirs(survey_dir, exist_ok=True)

        # Použijeme robustní JSON parser místo XML LSS
        json_path = os.path.join(survey_dir, "structure.json")
        enhanced_json_path = os.path.join(survey_dir, "structure_enhanced.json")

        # Pokud existuje vylepšená struktura, použijeme ji
        if os.path.exists(enhanced_json_path):
            logger.info(f"Používám existující vylepšenou strukturu: {enhanced_json_path}")
            return enhanced_json_path

        # Jinak použijeme robustní parser pro vytvoření vylepšené struktury
        logger.info(f"Vytvářím vylepšenou strukturu pomocí robustního parseru")
        return self.get_survey_structure_via_api(survey_id)

    def download_lss_file(self, survey_id: str) -> bool:
        """
        Stáhne LSS soubor průzkumu přes API

        Args:
            survey_id: ID průzkumu

        Returns:
            True pokud se podařilo stáhnout, False jinak
        """
        logger.info(f"Stahuji LSS strukturu pro průzkum {survey_id}")

        try:
            # Vytvoření podadresáře pro průzkum
            survey_dir = os.path.join(self.data_dir, survey_id)
            os.makedirs(survey_dir, exist_ok=True)

            # Stažení LSS struktury - zkusíme různé možné parametry
            # Možnost 1: export_structure s formátem
            lss_data = self._make_request("export_structure", [
                self.get_session_key(),
                survey_id,
                'lss'  # Formát LSS (XML)
            ])

            # Pokud to nefunguje, zkusíme bez formátu
            if not lss_data:
                logger.info("Zkouším export_structure bez formátu...")
                lss_data = self._make_request("export_structure", [
                    self.get_session_key(),
                    survey_id
                ])

            if not lss_data:
                logger.error(f"Nepodařilo se stáhnout LSS strukturu pro průzkum {survey_id}")
                return False

            # Dekódování base64 dat
            import base64
            lss_content = base64.b64decode(lss_data).decode('utf-8')

            # Uložení do XML LSS souboru (standard)
            lss_path = os.path.join(survey_dir, "structure.lss")
            with open(lss_path, 'w', encoding='utf-8') as f:
                f.write(lss_content)

            logger.info(f"✅ XML LSS soubor uložen: {lss_path}")
            return True

        except Exception as e:
            logger.error(f"❌ Chyba při stahování LSS souboru: {str(e)}")
            return False

    def get_survey_structure_via_api(self, survey_id: str) -> dict:
        """
        Získání struktury průzkumu přes API s robustním parserem

        Args:
            survey_id: ID průzkumu

        Returns:
            Dict se strukturou průzkumu
        """
        logger.info(f"Získávám strukturu průzkumu {survey_id} přes robustní JSON parser")

        # Použijeme robustní JSON parser na existující JSON strukturu
        survey_dir = os.path.join(self.data_dir, survey_id)
        json_path = os.path.join(survey_dir, "structure.json")

        # Nejprve získáme JSON strukturu přes API
        fallback_result = self.get_survey_structure_via_api_fallback(survey_id)

        # Zkontrolujeme, jestli existuje JSON soubor (může mít příponu .lss)
        lss_as_json_path = os.path.join(survey_dir, "structure.lss")

        if fallback_result and (os.path.exists(json_path) or os.path.exists(lss_as_json_path)):
            # Použijeme existující soubor
            actual_json_path = json_path if os.path.exists(json_path) else lss_as_json_path
            try:
                from robust_json_parser import RobustJSONParser

                parser = RobustJSONParser()
                survey = parser.parse_json_file(actual_json_path)

                if survey:
                    logger.info(f"✅ Struktura načtena robustním JSON parserem")

                    # Převod na kompatibilní formát s vylepšenou strukturou
                    structure = {
                        'survey_id': survey_id,
                        'groups': [],
                        'questions': [],
                        'timestamp': datetime.now().isoformat(),
                        'metadata': survey.metadata.__dict__,
                        'parser_version': 'robust_json_v1.0'
                    }

                    # Převod skupin s robustní logikou
                    for group in survey.groups:
                        group_dict = {
                            'gid': group.gid,
                            'group_name': group.group_name,
                            'group_order': group.group_order,
                            'description': group.description or '',
                            'questions': []
                        }

                        # Převod otázek ve skupině
                        for question in group.questions:
                            question_dict = self._convert_question_to_dict(question)
                            group_dict['questions'].append(question_dict)
                            structure['questions'].append(question_dict)

                        structure['groups'].append(group_dict)

                    # Uložení vylepšené struktury
                    enhanced_json_path = os.path.join(survey_dir, "structure_enhanced.json")
                    with open(enhanced_json_path, 'w', encoding='utf-8') as f:
                        json.dump(structure, f, ensure_ascii=False, indent=2)

                    logger.info(f"✅ Vylepšená struktura uložena do: {enhanced_json_path}")
                    return enhanced_json_path

            except Exception as e:
                logger.error(f"❌ Chyba při použití robustního JSON parseru: {str(e)}")

        # Fallback na původní API přístup
        logger.info(f"Používám fallback API přístup pro průzkum {survey_id}")
        return fallback_result

    def _convert_question_to_dict(self, question) -> dict:
        """Převede Question objekt na slovník kompatibilní s existujícím kódem"""
        question_dict = {
            'qid': question.qid,
            'type': question.type,
            'title': question.title,
            'question': question.text,
            'help': question.help_text or '',
            'mandatory': 'Y' if question.mandatory else 'N',
            'other': 'Y' if question.other else 'N',
            'gid': question.gid,
            'question_order': question.question_order,
            'relevance': question.relevance,
            'scale_id': question.scale_id,
            'language': question.language,
            'subquestions': [],
            'answers': [],
            'attributes': question.attributes or {}
        }

        # Převod subotázek
        for subq in question.subquestions:
            subq_dict = {
                'qid': subq.qid,
                'parent_qid': subq.parent_qid,
                'title': subq.title,
                'question': subq.text,
                'scale_id': subq.scale_id,
                'question_order': subq.sortorder
            }
            question_dict['subquestions'].append(subq_dict)

        # Převod odpovědí
        for answer in question.answers:
            answer_dict = {
                'qid': answer.qid,
                'code': answer.code,
                'answer': answer.text,
                'sortorder': answer.sortorder,
                'scale_id': answer.scale_id,
                'language': answer.language
            }
            question_dict['answers'].append(answer_dict)

        return question_dict

    def get_survey_structure_via_api_fallback(self, survey_id: str) -> dict:
        """
        Původní API přístup pro získání struktury průzkumu (fallback)

        Args:
            survey_id: ID průzkumu

        Returns:
            Dict se strukturou průzkumu
        """
        logger.info(f"Získávám strukturu průzkumu {survey_id} přes původní API")

        # Vytvoření podadresáře pro průzkum
        survey_dir = os.path.join(self.data_dir, survey_id)
        os.makedirs(survey_dir, exist_ok=True)

        # Získání všech otázek
        questions = self._make_request("list_questions", [
            self.get_session_key(),
            survey_id
        ])

        # Získání skupin otázek
        groups = self._make_request("list_groups", [
            self.get_session_key(),
            survey_id
        ])
        
        # Seřadit skupiny podle group_order (pokud je k dispozici) nebo podle gid
        if groups:
            groups = sorted(groups, key=lambda g: (
                int(g.get('group_order', 0)),  # Primární řazení podle group_order
                int(g.get('gid', 0))           # Sekundární řazení podle gid
            ))
        
        # Sestavení struktury
        structure = {
            'survey_id': survey_id,
            'groups': [],
            'questions': [],
            'timestamp': datetime.now().isoformat()
        }
        
        # Přidání skupin (už seřazených)
        for group_index, group in enumerate(groups):
            structure['groups'].append({
                'gid': group['gid'],
                'group_name': group['group_name'],
                'group_order': int(group.get('group_order', group_index)),  # Převést na int
                'description': group.get('description', ''),
                'questions': []
            })
            
        # Přidání otázek do skupin
        for question in questions:
            group = next((g for g in structure['groups'] if g['gid'] == question['gid']), None)
            if group:
                # OPRAVA: Nebudeme volat _get_question_answers - odpovědi jsou v CSV!
                # CSV už obsahuje všechny odpovědi, nepotřebujeme je stahovat znovu
                answers = {}
                
                # OPRAVA: Použijeme get_question_properties pouze pro skutečné hlavní otázky
                # které začínají "G" a mají typ M, L, F, Y, S (ne subotázky SQ001, atd.)
                main_question_types = ['M', 'L', 'F', 'Y', 'S']
                question_title = question.get('title', '')
                is_main_question = (
                    question.get('type') in main_question_types and
                    question_title.startswith('G') and
                    'Q' in question_title
                )

                full_properties = None

                if is_main_question:
                    try:
                        logger.info(f"Získávám kompletní vlastnosti pro hlavní otázku {question['qid']} ({question_title})")
                        # Bez rate limiting - není potřeba pro malý počet hlavních otázek
                        full_properties = self.get_question_properties(question['qid'], delay=0.1)
                    except Exception as e:
                        logger.warning(f"Nepodařilo se získat kompletní vlastnosti pro otázku {question['qid']}: {e}")
                        full_properties = None

                try:
                    if full_properties:
                        # Použijeme kompletní data z get_question_properties
                        properties = full_properties

                        # Přidáme základní data z list_questions jako fallback
                        properties.update({
                            'qid': question.get('qid'),
                            'type': question.get('type'),
                            'title': question.get('title'),
                            'question': question.get('question'),
                            'gid': question.get('gid'),
                            'mandatory': question.get('mandatory', 'N'),
                            'other': question.get('other', 'N'),
                            'help': question.get('help', ''),
                            'language': question.get('language', 'cs')
                        })
                    else:
                        # Fallback na základní data
                        properties = {
                            'qid': question.get('qid'),
                            'type': question.get('type'),
                            'title': question.get('title'),
                            'question': question.get('question'),
                            'gid': question.get('gid'),
                            'mandatory': question.get('mandatory', 'N'),
                            'other': question.get('other', 'N'),
                            'help': question.get('help', ''),
                            'language': question.get('language', 'cs')
                        }
                except Exception as e:
                    logger.warning(f"Nepodařilo se získat kompletní vlastnosti pro otázku {question['qid']}: {e}")
                    # Fallback na základní data
                    properties = {
                        'qid': question.get('qid'),
                        'type': question.get('type'),
                        'title': question.get('title'),
                        'question': question.get('question'),
                        'gid': question.get('gid'),
                        'mandatory': question.get('mandatory', 'N'),
                        'other': question.get('other', 'N'),
                        'help': question.get('help', ''),
                        'language': question.get('language', 'cs')
                    }

                # Vytvoříme strukturu otázky s kompletními daty
                question_data = {
                    'qid': question['qid'],
                    'title': question['title'],
                    'question': question['question'],
                    'type': question['type'],
                    'answers': answers,
                    'properties': properties
                }

                # Přidáme kompletní data z get_question_properties na top level
                if full_properties:
                    available_answers = full_properties.get('available_answers', {})
                    subquestions = full_properties.get('subquestions', {})

                    logger.info(f"   ✅ Přidávám API data: {len(available_answers)} available_answers, {len(subquestions)} subquestions")

                    question_data.update({
                        'available_answers': available_answers,
                        'subquestions': subquestions,
                        'attributes': full_properties.get('attributes', {}),
                        'parent_qid': full_properties.get('parent_qid', 0)
                    })
                else:
                    logger.warning(f"   ❌ Žádná API data pro otázku {question['title']}")

                group['questions'].append(question_data)
        
        # Uložení struktury do JSON souboru (fallback API přístup)
        json_path = os.path.join(survey_dir, "structure.json")
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(structure, f, ensure_ascii=False, indent=2)

        logger.info(f"✅ Struktura z API uložena do: {json_path}")
        return json_path

    def _format_groups_to_lss(self, structure: dict) -> str:
        """Převede strukturu skupin otázek do LSS formátu"""
        if not structure or 'groups' not in structure:
            return ""
            
        lss_groups = []
        for group in structure['groups']:
            questions = []
            for question in group.get('questions', []):
                question_xml = f"""
                <question id="{question['qid']}" type="{question['type']}">
                    <title>{question['title']}</title>
                    <question_text>{question['question']}</question_text>
                    <answers>
                        {self._format_answers_to_lss(question.get('answers', {}))}
                    </answers>
                </question>"""
                questions.append(question_xml)
                
            group_xml = f"""
            <group id="{group['gid']}">
                <group_name>{group['group_name']}</group_name>
                <questions>
                    {''.join(questions)}
                </questions>
            </group>"""
            lss_groups.append(group_xml)
            
        return '\n'.join(lss_groups)
        
    def _format_answers_to_lss(self, answers: dict) -> str:
        """Převede odpovědi na LSS formát"""
        if not answers:
            return ""
            
        answer_items = []
        for code, text in answers.items():
            answer_items.append(f'<answer code="{code}">{text}</answer>')
            
        return '\n'.join(answer_items)

    def _get_question_answers(self, question_id: str, delay: float = 0.5) -> dict:
        """Získání možností odpovědí pro otázku s rate limiting"""
        import time

        # Rate limiting - pauza před voláním
        if delay > 0:
            time.sleep(delay)

        answers = self._make_request("list_answers", [
            self.get_session_key(),
            question_id
        ])
        
        if not answers:
            return {}
            
        return {a['code']: a['answer'] for a in answers}
        
    def get_question_properties(self, question_id: str, properties: list = None, delay: float = 0.5) -> dict:
        """
        Získání vlastností otázky s rate limiting

        Args:
            question_id: ID otázky
            properties: Seznam požadovaných vlastností (None = všechny)
            delay: Pauza v sekundách před voláním (default: 0.5s)

        Returns:
            Dict s vlastnostmi otázky
        """
        import time

        # Rate limiting - pauza před voláním
        if delay > 0:
            time.sleep(delay)

        logger.info(f"Získávám vlastnosti otázky {question_id}")

        # Opravené parametry pro LimeSurvey API
        params = [self.get_session_key(), question_id]

        # Přidáme properties pouze pokud jsou specifikovány
        if properties:
            params.append(properties)

        return self._make_request("get_question_properties", params)

    def release_session(self):
        """Uvolnění session key"""
        if self.session_key:
            self._make_request("release_session_key", [self.session_key])
            self.session_key = None

    def get_fieldmap(self, survey_id: str) -> dict:
        """
        Získání mapování polí průzkumu
        
        Args:
            survey_id: ID průzkumu
            
        Returns:
            Dict s mapováním polí
        """
        logger.info(f"Získávám fieldmap pro průzkum {survey_id}")
        return self._make_request("get_fieldmap", [
            self.get_session_key(),
            survey_id
        ])

    def list_surveys(self) -> list:
        """
        Získání seznamu dostupných průzkumů
        
        Returns:
            List s informacemi o průzkumech
        """
        logger.info("Získávám seznam průzkumů")
        return self._make_request("list_surveys", [
            self.get_session_key()
        ]) or []
