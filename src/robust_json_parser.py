# -*- coding: utf-8 -*-

"""
LimeSurvey JSON Structure Parser - Robustní parser pro JSON struktury

Tento modul poskytuje funkcionalitu pro parsování JSON struktury průzkumu LimeSurvey.
Využívá stejnou logiku jako XML parser, ale pracuje s JSON daty z API.

Klíčové vlastnosti:
- Parsování JSON struktury z LimeSurvey API
- Extrakce skupin otázek a jejich seřazení dle pořadí v průzkumu
- Extrakce otázek, v<PERSON><PERSON><PERSON><PERSON> jejich textů, typů a nastavení
- Správné zpracován<PERSON> pod<PERSON> (subquestions) a odpovědí (answers)
- Sestavení finální datové struktury kompatibilní s XML parserem
- Robustní zpracování všech typů otázek LimeSurvey

Autor: <PERSON>: Leden 2025
Integrace do LIMWRAPP: Leden 2025
"""

import json
import logging
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
from dataclasses import dataclass, asdict
import re
from datetime import datetime

# Import datových tříd z XML parseru pro kompatibilitu
from robust_lss_parser import (
    SurveyMetadata, QuestionAttribute, Answer, Subquestion, 
    Question, Group, Survey
)

# Nastavení loggeru
logger = logging.getLogger(__name__)

class RobustJSONParser:
    """
    Robustní parser pro LimeSurvey JSON struktury.
    
    Tento parser je navržen pro zpracování JSON dat z LimeSurvey API
    a zachování přesné hierarchie a pořadí prvků.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def parse_json_file(self, json_file_path: str) -> Optional[Survey]:
        """
        Parsuje JSON soubor se strukturou průzkumu a vrátí strukturovaná data
        
        Args:
            json_file_path: Cesta k JSON souboru
            
        Returns:
            Survey objekt nebo None při chybě
        """
        try:
            self.logger.info(f"🔍 Parsování JSON struktury: {json_file_path}")
            
            # Kontrola existence souboru
            if not Path(json_file_path).exists():
                self.logger.error(f"❌ JSON soubor neexistuje: {json_file_path}")
                return None
            
            # Načtení JSON dat
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.info(f"📄 Načten JSON soubor")
            
            # Parsování metadat
            metadata = self._parse_metadata(data)
            if not metadata:
                self.logger.error("❌ Nepodařilo se načíst metadata průzkumu")
                return None
            
            # Parsování skupin a otázek
            groups = self._parse_groups_and_questions(data)
            self.logger.info(f"📁 Načteno {len(groups)} skupin")
            
            # Extrakce všech otázek pro kompatibilitu
            all_questions = []
            for group in groups:
                all_questions.extend(group.questions)
            
            self.logger.info(f"❓ Celkem otázek: {len(all_questions)}")
            
            # Vytvoření finální struktury
            survey = Survey(
                metadata=metadata,
                groups=groups,
                questions=all_questions
            )
            
            self.logger.info(f"✅ Parsování dokončeno úspěšně")
            return survey
            
        except Exception as e:
            self.logger.error(f"❌ Chyba při parsování JSON: {str(e)}")
            return None
    
    def _parse_metadata(self, data: Dict[str, Any]) -> Optional[SurveyMetadata]:
        """Parsuje metadata průzkumu z JSON dat"""
        try:
            # Extrakce základních údajů
            survey_id = str(data.get('survey_id', ''))

            # Metadata mohou být v různých místech - zkusíme různé varianty
            metadata_dict = data.get('metadata', {})

            # Pokud metadata nejsou v samostatném objektu, zkusíme je najít v root objektu
            if not metadata_dict:
                metadata_dict = data

            title = metadata_dict.get('title', f'Průzkum {survey_id}')
            description = metadata_dict.get('description', '')
            language = metadata_dict.get('language', 'cs')
            created = metadata_dict.get('created')
            modified = metadata_dict.get('modified')
            active = metadata_dict.get('active', True)
            expires = metadata_dict.get('expires')
            startdate = metadata_dict.get('startdate')

            return SurveyMetadata(
                sid=survey_id,
                title=title,
                description=description,
                language=language,
                created=created,
                modified=modified,
                active=active,
                expires=expires,
                startdate=startdate
            )

        except Exception as e:
            self.logger.error(f"❌ Chyba při parsování metadat: {str(e)}")
            return None
    
    def _parse_groups_and_questions(self, data: Dict[str, Any]) -> List[Group]:
        """Parsuje skupiny a otázky z JSON dat"""
        groups = []
        
        try:
            # Skupiny mohou být v 'groups' klíči
            groups_data = data.get('groups', [])
            
            for group_data in groups_data:
                group = self._parse_single_group(group_data)
                if group:
                    groups.append(group)
            
            # Seřazení podle group_order
            groups.sort(key=lambda g: g.group_order)
            
            return groups
            
        except Exception as e:
            self.logger.error(f"❌ Chyba při parsování skupin: {str(e)}")
            return []
    
    def _parse_single_group(self, group_data: Dict[str, Any]) -> Optional[Group]:
        """Parsuje jednu skupinu z JSON dat"""
        try:
            gid = str(group_data.get('gid', ''))
            if not gid:
                return None

            group_name = group_data.get('group_name', f'Skupina {gid}')
            group_order = int(group_data.get('group_order', 0))
            description = group_data.get('description', '')
            language = group_data.get('language', 'cs')

            # Vytvoření skupiny
            group = Group(
                gid=gid,
                group_name=group_name,
                group_order=group_order,
                description=description,
                language=language
            )

            # Parsování otázek ve skupině
            questions_data = group_data.get('questions', [])
            for question_data in questions_data:
                question = self._parse_single_question(question_data, gid)
                if question:
                    group.questions.append(question)

            # Seřazení otázek podle question_order (pokud existuje)
            group.questions.sort(key=lambda q: getattr(q, 'question_order', 0))

            return group

        except Exception as e:
            self.logger.error(f"❌ Chyba při parsování skupiny: {str(e)}")
            return None

    def _parse_single_question(self, question_data: Dict[str, Any], group_id: str) -> Optional[Question]:
        """Parsuje jednu otázku z JSON dat"""
        try:
            qid = str(question_data.get('qid', ''))
            if not qid:
                return None

            # Základní údaje - zkusíme různé zdroje
            properties = question_data.get('properties', {})

            question_type = question_data.get('type') or properties.get('type', 'T')
            title = question_data.get('title') or properties.get('title', f'Q{qid}')
            text = question_data.get('question') or properties.get('question', '')
            help_text = question_data.get('help') or properties.get('help', '')

            # question_order může být v různých místech
            question_order = 0
            if 'question_order' in question_data:
                question_order = int(question_data['question_order'])
            elif 'question_order' in properties:
                question_order = int(properties['question_order'])

            relevance = question_data.get('relevance') or properties.get('relevance', '1')
            scale_id = int(question_data.get('scale_id', 0) or properties.get('scale_id', 0))
            language = question_data.get('language') or properties.get('language', 'cs')

            # Boolean hodnoty
            mandatory_val = question_data.get('mandatory') or properties.get('mandatory', 'N')
            mandatory = mandatory_val == 'Y' if mandatory_val else False

            other_val = question_data.get('other') or properties.get('other', 'N')
            other = other_val == 'Y' if other_val else False

            # Vytvoření otázky
            question = Question(
                qid=qid,
                type=question_type,
                title=title,
                text=text,
                help_text=help_text,
                mandatory=mandatory,
                other=other,
                gid=group_id,
                question_order=question_order,
                relevance=relevance,
                scale_id=scale_id,
                language=language
            )

            # Parsování subotázek (pokud existují)
            subquestions_data = question_data.get('subquestions', [])
            for subq_data in subquestions_data:
                subquestion = self._parse_single_subquestion(subq_data, qid)
                if subquestion:
                    question.subquestions.append(subquestion)

            # Parsování odpovědí (pokud existují)
            answers_data = question_data.get('answers', {})
            if isinstance(answers_data, dict):
                for answer_code, answer_text in answers_data.items():
                    answer = Answer(
                        qid=qid,
                        code=str(answer_code),
                        text=str(answer_text),
                        sortorder=0,
                        scale_id=0,
                        language=language
                    )
                    question.answers.append(answer)
            elif isinstance(answers_data, list):
                for answer_data in answers_data:
                    answer = self._parse_single_answer(answer_data, qid)
                    if answer:
                        question.answers.append(answer)

            # Parsování atributů
            attributes_data = question_data.get('attributes', {})
            question.attributes = attributes_data

            # Seřazení subotázek a odpovědí
            if question.subquestions:
                question.subquestions.sort(key=lambda sq: getattr(sq, 'sortorder', 0))
            if question.answers:
                question.answers.sort(key=lambda a: getattr(a, 'sortorder', 0))

            return question

        except Exception as e:
            self.logger.error(f"❌ Chyba při parsování otázky {qid}: {str(e)}")
            return None

    def _parse_single_subquestion(self, subq_data: Dict[str, Any], parent_qid: str) -> Optional[Subquestion]:
        """Parsuje jednu subotázku z JSON dat"""
        try:
            qid = str(subq_data.get('qid', ''))
            if not qid:
                return None

            title = subq_data.get('title', f'SQ{qid}')
            text = subq_data.get('question', '')
            scale_id = int(subq_data.get('scale_id', 0))
            sortorder = int(subq_data.get('question_order', 0))

            return Subquestion(
                qid=qid,
                parent_qid=parent_qid,
                title=title,
                text=text,
                scale_id=scale_id,
                sortorder=sortorder
            )

        except Exception as e:
            self.logger.error(f"❌ Chyba při parsování subotázky: {str(e)}")
            return None

    def _parse_single_answer(self, answer_data: Dict[str, Any], parent_qid: str) -> Optional[Answer]:
        """Parsuje jednu odpověď z JSON dat"""
        try:
            code = str(answer_data.get('code', ''))
            if not code:
                return None

            text = answer_data.get('answer', '')
            sortorder = int(answer_data.get('sortorder', 0))
            scale_id = int(answer_data.get('scale_id', 0))
            language = answer_data.get('language', 'cs')

            return Answer(
                qid=parent_qid,
                code=code,
                text=text,
                sortorder=sortorder,
                scale_id=scale_id,
                language=language
            )

        except Exception as e:
            self.logger.error(f"❌ Chyba při parsování odpovědi: {str(e)}")
            return None

    def get_question_types_summary(self, survey: Survey) -> Dict[str, int]:
        """Vrátí přehled typů otázek v průzkumu"""
        type_counts = {}
        for question in survey.questions:
            qtype = question.type
            type_counts[qtype] = type_counts.get(qtype, 0) + 1
        return type_counts

    def export_to_json(self, survey: Survey, output_path: str) -> bool:
        """Exportuje survey do JSON souboru"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(survey.to_dict(), f, ensure_ascii=False, indent=2)
            self.logger.info(f"✅ Survey exportován do: {output_path}")
            return True
        except Exception as e:
            self.logger.error(f"❌ Chyba při exportu do JSON: {str(e)}")
            return False
