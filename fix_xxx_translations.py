#!/usr/bin/env python3
"""
Skript pro nahrazení XXX v translations_cs-CZ.json skutečnými názvy skupin
"""

import json
import pandas as pd
import re

def load_question_mapping():
    """Načte mapování otázek a skupin"""
    mapping_path = "data/dotazniky.urad.online/548754/question_mapping.csv"
    df = pd.read_csv(mapping_path)
    
    # Vytvoříme mapování question_code -> group_name
    question_to_group = {}
    for _, row in df.iterrows():
        question_code = row['question_code']
        group_name = row['group_name']
        
        # Vyčistíme název skupiny (odstraníme číslo a dvojtečku)
        if ':' in group_name:
            clean_group_name = group_name.split(':', 1)[1].strip()
        else:
            clean_group_name = group_name
            
        question_to_group[question_code] = clean_group_name
    
    return question_to_group

def fix_xxx_in_translations():
    """Nahradí XXX v translations_cs-CZ.json skutečnými názvy skupin"""
    
    # Načteme mapování
    question_to_group = load_question_mapping()
    
    # Načteme translations
    translations_path = "data/dotazniky.urad.online/548754/translations_cs-CZ.json"
    with open(translations_path, 'r', encoding='utf-8') as f:
        translations = json.load(f)
    
    # Projdeme question_names a nahradíme XXX
    changes_made = 0
    
    for question_code, question_text in translations.get('question_names', {}).items():
        if 'XXX' in question_text:
            # Najdeme skupinu pro tuto otázku
            group_name = question_to_group.get(question_code)
            
            if group_name:
                # Nahradíme XXX názvem skupiny
                new_text = question_text.replace('XXX', group_name)
                translations['question_names'][question_code] = new_text
                changes_made += 1
                
                print(f"✅ {question_code}: '{question_text}' → '{new_text}'")
            else:
                print(f"❌ {question_code}: Skupina nenalezena pro '{question_text}'")
    
    # Uložíme opravené translations
    if changes_made > 0:
        with open(translations_path, 'w', encoding='utf-8') as f:
            json.dump(translations, f, ensure_ascii=False, indent=2)
        
        print(f"\n🎉 Úspěšně nahrazeno {changes_made} výskytů XXX!")
        print(f"📁 Soubor uložen: {translations_path}")
    else:
        print("\nℹ️  Žádné XXX k nahrazení nenalezeno")

if __name__ == "__main__":
    print("🔄 Nahrazuji XXX v translations_cs-CZ.json...")
    fix_xxx_in_translations()
    print("✅ Hotovo!")
