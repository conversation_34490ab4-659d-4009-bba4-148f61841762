# Analýza chyb v parseru LSS a zpracování dat - Průzkum 548754

## Hlavní problém: Nerozpoznávání opakujících se otázek pro různé lokality

### Popis problému
Ve zpracovávaném průzkumu 548754 se opakuje celkem 8 skupin se stejnými otázkami pro různé lokality:
- <PERSON><PERSON> (skupina 88)
- <PERSON><PERSON> (skupina 97) 
- <PERSON><PERSON><PERSON><PERSON> (skupina 98)
- <PERSON><PERSON> (skupina 99)
- <PERSON><PERSON><PERSON><PERSON> (skupina 100)
- <PERSON><PERSON><PERSON><PERSON> (skupina 101)
- Cholupice (skupina 102)
- <PERSON><PERSON><PERSON> (skupina 103)

Každá skupina obsahuje stejné otázky:
1. "Čeho si v této lokalitě nejvíce ceníte?"
2. "Co Vám v této lokalitě chybí?"
3. "J<PERSON> jste celkově spokojen/a s touto lokalitou?"
4. "<PERSON><PERSON><PERSON> vztah k lokalitě je:"

### Konkrétní chyby identifikované:

#### 1. **KRITICKÁ CHYBA: Nesprávné mapování otázek v CSV**
- **Problém**: V CSV jsou sloupce jako `r306q0[SQ001]`, `r664q0[SQ001]`, `r373q0[SQ001]`, atd.
- **Očekávané**: V question_mapping.csv jsou pouze `G3Q00001[SQ001]`, `G3Q00002[SQ001]`
- **Důsledek**: Parser nerozpoznává, že různé kódy otázek (r306q0, r664q0, atd.) jsou stejné otázky pro různé lokality
- **Lokace**: `src/data_transformer.py`, funkce `generate_question_mapping()`

#### 2. **CHYBA: Chybějící hlavní otázky v mapování**
- **Problém**: Hlavní otázky typu "Čeho si v této lokalitě nejvíce ceníte?" nejsou v question_mapping.csv jako hlavní otázky
- **Očekávané**: Měly by být 8 hlavních otázek (pro každou lokalitu jedna)
- **Skutečnost**: Jsou pouze subotázky G3Q00001[SQ001], G3Q00001[SQ002], atd.
- **Lokace**: `src/lss_parser.py`, funkce `_parse_hierarchical_questions()`

#### 3. **CHYBA: Nesprávná identifikace hlavních vs. subotázek**
- **Problém**: Parser nerozpoznává, že otázky pro různé lokality jsou hlavní otázky, ne subotázky
- **Důsledek**: V translations.json chybí správné názvy s lokalitami
- **Lokace**: `src/lss_parser.py`, funkce `_attach_subquestions_and_answers()`

#### 4. **CHYBA: Nesprávné generování překladů**
- **Problém**: V translations_cs-CZ.json jsou technické kódy (SQ015, SQ016) místo názvů lokalit
- **Příklad**: `"G3Q00002[SQ015]": "Co Vám v této lokalitě chybí? - SQ015"` 
- **Očekávané**: `"G3Q00002[SQ015]": "Co chybí v lokalitě Staré Modřany?"`
- **Lokace**: `src/translation_manager.py`, funkce `generate_translation_template()`

#### 5. **CHYBA: Chybějící propojení LSS struktury s CSV daty**
- **Problém**: Parser nerozpoznává, že různé question_codes v CSV odpovídají stejným otázkám v LSS
- **Příklad**: 
  - LSS: `"title": "G3Q00001"` (Staré Modřany)
  - CSV: `r306q0[SQ001]` (Nové Modřany)
  - CSV: `r664q0[SQ001]` (Beránek)
- **Lokace**: `src/data_transformer.py`, funkce `generate_question_mapping()`

### Dopad na workflow:
1. **Translations**: Chybí správné názvy otázek s lokalitami
2. **Chart generation**: Grafy nemají správné názvy
3. **Data processing**: Nesprávné mapování dat z CSV
4. **User experience**: Uživatel nevidí, ke které lokalitě se otázka vztahuje

### Potřebné opravy:
1. Upravit parser LSS pro rozpoznání opakujících se otázek
2. Vytvořit správné mapování mezi LSS a CSV kódy
3. Opravit generování překladů s názvy lokalit
4. Implementovat diagnostické nástroje pro kontrolu struktury

### Priorita: **KRITICKÁ** - blokuje správné zpracování dat a generování grafů
